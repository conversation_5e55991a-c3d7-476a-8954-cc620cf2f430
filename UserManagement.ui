<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserManagement</class>
 <widget class="QDialog" name="UserManagement">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>671</width>
    <height>482</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(255, 255, 255);</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>2</number>
     </property>
     <widget class="QWidget" name="tab_5">
      <attribute name="title">
       <string>用户展示</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QTableWidget" name="tableWidget">
         <column>
          <property name="text">
           <string>用户ID</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>用户名</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>密码</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>联系方式</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>家庭地址</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>紧急联系人电话</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>新增用户</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="2" column="1">
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_addOk">
           <property name="text">
            <string>新增</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_addCancel">
           <property name="text">
            <string>取消</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="3" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <layout class="QGridLayout" name="gridLayout_3">
         <item row="0" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>用户名：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="le_addUserName"/>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>密码：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="le_addUserPassword"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>联系方式：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="le_addPhoneNumber"/>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>家庭地址：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="le_addHomeAddress"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>紧急联系人电话:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="le_addOtherPhoneNumber"/>
         </item>
        </layout>
       </item>
       <item row="0" column="0">
        <spacer name="horizontalSpacer_10">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>修改用户</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_7">
       <item row="1" column="1">
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <spacer name="horizontalSpacer_6">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_updateOk">
           <property name="text">
            <string>修改</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_updateCancel">
           <property name="text">
            <string>取消</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="4" column="0">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>家庭地址：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="1" colspan="2">
          <widget class="QLineEdit" name="le_updatePassword"/>
         </item>
         <item row="1" column="1" colspan="2">
          <widget class="QLineEdit" name="le_updateUserName"/>
         </item>
         <item row="0" column="0" colspan="2">
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>用户ID：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLineEdit" name="le_updateUserId">
           <property name="text">
            <string/>
           </property>
           <property name="placeholderText">
            <string>输入自动补全</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1" colspan="2">
          <widget class="QLineEdit" name="le_updateOtherPhoneNumber"/>
         </item>
         <item row="4" column="1" colspan="2">
          <widget class="QLineEdit" name="le_updateHomeAddress"/>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>联系方式：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>紧急联系人电话:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>用户名：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>密码：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="1" colspan="2">
          <widget class="QLineEdit" name="le_updatePhoneNumber"/>
         </item>
        </layout>
       </item>
       <item row="3" column="0">
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="0">
        <spacer name="horizontalSpacer_11">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>删除用户</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="3" column="0">
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="1">
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btn_delOk">
           <property name="text">
            <string>确定删除</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_delCancel">
           <property name="text">
            <string>取消</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_9">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_11">
           <property name="text">
            <string>请输入要删除人员的ID：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_delUserId"/>
         </item>
        </layout>
       </item>
       <item row="0" column="0">
        <spacer name="horizontalSpacer_12">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
