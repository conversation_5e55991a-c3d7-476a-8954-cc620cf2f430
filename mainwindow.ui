<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1434</width>
    <height>745</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <layout class="QGridLayout" name="gridLayout_8" columnstretch="1,6">
    <item row="1" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_3" stretch="4,6">
      <item>
       <widget class="QScrollArea" name="scrollArea">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(234, 234, 234);
</string>
        </property>
        <property name="widgetResizable">
         <bool>true</bool>
        </property>
        <widget class="QWidget" name="scrollAreaWidgetContents">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>258</width>
           <height>244</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="3" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_14">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="text">
               <string>保持时间(s)：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="le_keepTime">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_15">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="text">
               <string>高压值(kv)：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="le_highPressureValue">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="5" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_19">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="text">
               <string>试验次数(次)：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="le_numberOfTrials">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_12">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="text">
               <string>试验方式：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="cb_testMethod">
              <property name="font">
               <font>
                <family>Agency FB</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>无</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>验证性试验</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>破坏性试验</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item row="2" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_18">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="text">
               <string>SFL(KN)：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="le_SFLValue">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>350</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>控制方案：</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_3">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QTextEdit" name="te_controlScheme">
           <property name="readOnly">
            <bool>false</bool>
           </property>
           <property name="html">
            <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'SimSun'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'SimSun'; font-size:12pt; font-weight:600;&quot;&gt;这里是系统在进行试验过程中，记录的操作流程，不能人为修改&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="1">
     <layout class="QVBoxLayout" name="verticalLayout_2" stretch="7,2">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout" stretch="1,9">
        <item>
         <widget class="QWidget" name="widget_5" native="true">
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QGridLayout" name="gridLayout">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="7">
            <widget class="QLabel" name="label_4">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>位移(mm):</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="4">
            <widget class="QLabel" name="label">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>拉力(KN)：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="5">
            <widget class="QLabel" name="la_pullVal">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>0.00</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="10">
            <widget class="QLabel" name="la_displacementVal">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>0.00</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="6">
            <spacer name="horizontalSpacer_6">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_5">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>当前电压(KV)：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="11">
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="la_currentVoltageVal">
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="text">
              <string>0.00</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <spacer name="horizontalSpacer_5">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_2" native="true">
          <layout class="QGridLayout" name="gridLayout_5" columnstretch="5,5">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="DisplacementTimeWidget" native="true">
             <property name="styleSheet">
              <string notr="true">background-color: rgb(245, 245, 245);</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_6">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <property name="spacing">
               <number>0</number>
              </property>
             </layout>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QWidget" name="KNTimeWidget" native="true">
             <property name="styleSheet">
              <string notr="true">background-color: rgb(245, 245, 245);</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_7">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <property name="spacing">
               <number>0</number>
              </property>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="5,1">
        <item>
         <widget class="QTableWidget" name="tableWidget">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <row>
           <property name="text">
            <string>    1     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <row>
           <property name="text">
            <string>    2     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <row>
           <property name="text">
            <string>    3     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <row>
           <property name="text">
            <string>    4     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <row>
           <property name="text">
            <string>    5     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <row>
           <property name="text">
            <string>    6     </string>
           </property>
           <property name="font">
            <font>
             <family>Agency FB</family>
             <pointsize>12</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
          </row>
          <column>
           <property name="text">
            <string>试验ID</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>报告编号</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>试验方式</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>SFL值 (KN)</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>保持时间(s)</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>拉力值(KN)</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>电压值(KV)</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>位移值(mm)</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>记录时间</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>试验操作人员</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>检测结果</string>
           </property>
          </column>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QLabel" name="label_6">
            <property name="font">
             <font>
              <family>Agency FB</family>
              <pointsize>16</pointsize>
              <weight>75</weight>
              <italic>false</italic>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>试验结论：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="cb_testConclusion">
            <property name="font">
             <font>
              <family>Agency FB</family>
              <pointsize>16</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <item>
             <property name="text">
              <string>无</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>合格</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>不合格</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </item>
    <item row="0" column="0" colspan="2">
     <widget class="QWidget" name="widget" native="true">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(245, 245, 245);</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="8">
        <widget class="GraphicTextButton" name="btn_startTheExperiment">
         <property name="text">
          <string>开始试验</string>
         </property>
        </widget>
       </item>
       <item row="0" column="11">
        <widget class="GraphicTextButton" name="btn_reportReferences">
         <property name="text">
          <string>报告录参</string>
         </property>
        </widget>
       </item>
       <item row="0" column="15">
        <widget class="GraphicTextButton" name="btn_userManagement">
         <property name="text">
          <string>用户管理</string>
         </property>
        </widget>
       </item>
       <item row="0" column="12">
        <widget class="GraphicTextButton" name="btn_generateReport">
         <property name="text">
          <string>生成报告</string>
         </property>
        </widget>
       </item>
       <item row="0" column="13">
        <widget class="GraphicTextButton" name="btn_printingReports">
         <property name="text">
          <string>打印报告</string>
         </property>
        </widget>
       </item>
       <item row="0" column="16">
        <widget class="GraphicTextButton" name="btn_logOut">
         <property name="text">
          <string>退出登录</string>
         </property>
        </widget>
       </item>
       <item row="0" column="9">
        <widget class="GraphicTextButton" name="btn_stopTheExperiment">
         <property name="text">
          <string>停止试验</string>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="GraphicTextButton" name="btn_startHighVoltage">
         <property name="text">
          <string>启动高压</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="GraphicTextButton" name="btn_pause">
         <property name="text">
          <string>暂停</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="GraphicTextButton" name="btn_closeHighVoltage">
         <property name="text">
          <string>关闭高压</string>
         </property>
        </widget>
       </item>
       <item row="0" column="7">
        <widget class="GraphicTextButton" name="btn_oneClickReset">
         <property name="text">
          <string>一键清零</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="GraphicTextButton" name="btn_reset">
         <property name="text">
          <string>复位</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="GraphicTextButton" name="btn_reduceStress">
         <property name="text">
          <string>卸载</string>
         </property>
        </widget>
       </item>
       <item row="0" column="6">
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="14">
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="2">
        <widget class="GraphicTextButton" name="btn_increasedPressure">
         <property name="text">
          <string>加载</string>
         </property>
        </widget>
       </item>
       <item row="0" column="10">
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1434</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>GraphicTextButton</class>
   <extends>QPushButton</extends>
   <header location="global">graphictextbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
