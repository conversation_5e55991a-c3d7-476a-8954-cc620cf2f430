#include "usermanager.h"
#include "ui_UserManagement.h"

//UserManagement需要设置到ui文件里面的QDialog
UserManager::UserManager(QWidget *parent) : QDialog(parent),ui(new Ui::UserManagement)
{
    ui->setupUi(this);
    this->setWindowTitle("用户管理");
    //设置表格为只读
    //    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);

    //支持选中行 / 高亮等：
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);

    //设置自动拉伸列宽：
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    ui->tabWidget->setCurrentIndex(0);

    initShowData();
    connect(ui->btn_addOk,&QPushButton::clicked,this,&UserManager::btnAddUser);
    connect(ui->btn_addCancel,&QPushButton::clicked,this,&UserManager::btnAddCancel);
    connect(ui->le_updateUserId,&QLineEdit::textChanged,this,&UserManager::leUpdateUserId);
    connect(ui->btn_updateOk,&QPushButton::clicked,this,&UserManager::btnUpdateOk);
    connect(ui->btn_updateCancel,&QPushButton::clicked,this,&UserManager::btnUpdateCancel);
    connect(ui->btn_delOk,&QPushButton::clicked,this,&UserManager::btnDelOk);
    connect(ui->btn_delCancel,&QPushButton::clicked,this,&UserManager::btnDelCancel);

}

UserManager::~UserManager()
{
    delete ui;
}

void UserManager::initShowData()
{
    try {
        ui->tableWidget->clear();
        //查询
        QString sql = "SELECT user_id, username,password, contact, home_address, emergency_contact FROM user_info where role = :role";

        QVariantMap params {{"role", "user"}};
        auto query = DatabaseManager::instance()->executeQuery(sql, params);
        ui->tableWidget->setColumnCount(6);
        ui->tableWidget->setHorizontalHeaderLabels(QStringList() << "用户ID" << "用户名"<<"密码" << "联系方式" << "家庭地址" << "紧急联系人");
        int row = 0;
        while (query.next()) {
            ui->tableWidget->insertRow(row); // 插入新的一行
            ui->tableWidget->setItem(row, 0, new QTableWidgetItem(query.value("user_id").toString()));
            ui->tableWidget->setItem(row, 1, new QTableWidgetItem(query.value("username").toString()));
            ui->tableWidget->setItem(row, 2, new QTableWidgetItem(query.value("password").toString()));
            ui->tableWidget->setItem(row, 3, new QTableWidgetItem(query.value("contact").toString()));
            ui->tableWidget->setItem(row, 4, new QTableWidgetItem(query.value("home_address").toString()));
            ui->tableWidget->setItem(row, 5, new QTableWidgetItem(query.value("emergency_contact").toString()));
            row++;
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("加载用户数据过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("加载用户数据过程中发生未知异常", LogLevel::Error);
    }
}

void UserManager::btnAddUser()
{
    try {
        if (ui->le_addUserName->text().trimmed().isEmpty()) {
            QMessageBox::warning(this, "提示", "用户名不能为空！");
            return;
        }
        if (ui->le_addUserPassword->text().trimmed().isEmpty()) {
            QMessageBox::warning(this, "提示", "密码不能为空！");
            return;
        }
        if (ui->le_addPhoneNumber->text().trimmed().isEmpty()) {
            QMessageBox::warning(this, "提示", "联系电话不能为空！");
            return;
        }
        //增加
        QString insertUserSql = R"(
                                INSERT INTO user_info (username, password, contact, home_address,emergency_contact, role)
                                VALUES (:username, :password, :contact, :home_address,:emergency_contact, :role)
                                )";

        QVariantMap userParams {
            {"username", ui->le_addUserName->text()},
            {"password", ui->le_addUserPassword->text()},  // 建议加密，例如 SHA-256
            {"contact", ui->le_addPhoneNumber->text()},
            {"home_address", ui->le_addHomeAddress->text()},
            {"emergency_contact", ui->le_addOtherPhoneNumber->text()},
            {"role", "user"}
        };
        DatabaseManager::instance()->execTransactional(insertUserSql, userParams);
        QMessageBox::information(this,"提示","已成功添加");
        initShowData();
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("添加用户过程发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统异常：" + QString::fromStdString(e.what()));
    } catch (...) {
        LogManager::instance()->writeLog("添加用户过程发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "发生未知异常，请查看日志");
    }
}

void UserManager::btnAddCancel()
{
    ui->le_addUserName->setText("");
    ui->le_addHomeAddress->setText("");
    ui->le_addPhoneNumber->setText("");
    ui->le_addUserPassword->setText("");
    ui->le_addOtherPhoneNumber->setText("");
    ui->tabWidget->setCurrentIndex(0);
}

void UserManager::leUpdateUserId()
{
    QString userId = ui->le_updateUserId->text().trimmed();
    if (userId.isEmpty()) return;

    QSqlQuery query;
    query.prepare("SELECT username, password, contact, home_address, emergency_contact FROM user_info WHERE user_id like :userId limit 1");
    query.bindValue(":userId", "%" + userId + "%");  // name 是用户输入的关键词
    if (query.exec() && query.next()) {
        ui->le_updateUserName->setText(query.value("username").toString());
        ui->le_updatePassword->setText(query.value("password").toString());
        ui->le_updatePhoneNumber->setText(query.value("contact").toString());
        ui->le_updateHomeAddress->setText(query.value("home_address").toString());
        ui->le_updateOtherPhoneNumber->setText(query.value("emergency_contact").toString());
    } else {
        QMessageBox::information(this,"提示","没有符合条件的用户ID");
    }
}

void UserManager::btnUpdateOk()
{
    try {
        //修改
        QString updateSql = R"(
                            UPDATE user_info SET username = :username,password = :password,contact = :contact,
                            home_address = :home_address,emergency_contact = :emergency_contact
                            WHERE user_id = :user_id)";

        QVariantMap params {
            {"username", ui->le_updateUserName->text().trimmed()},
            {"password", ui->le_updatePassword->text().trimmed()},
            {"contact",  ui->le_updatePhoneNumber->text().trimmed()},
            {"home_address", ui->le_updateHomeAddress->text().trimmed()},
            {"emergency_contact", ui->le_updateOtherPhoneNumber->text().trimmed()},
            {"user_id", ui->le_updateUserId->text()}  // 注意：这里是精确 user_id，而不是模糊匹配
        };

        DatabaseManager::instance()->execTransactional(updateSql, params);
        QMessageBox::information(this,"提示","已修改成功！");
        initShowData();
    }catch (const std::exception& e) {
        LogManager::instance()->writeLog("修改用户过程发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统异常：" + QString::fromStdString(e.what()));
    } catch (...) {
        LogManager::instance()->writeLog("修改用户过程发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "发生未知异常，请查看日志");
    }
}

void UserManager::btnUpdateCancel()
{
    ui->le_updateUserId->setText("");
    ui->le_updateUserName->setText("");
    ui->le_updateHomeAddress->setText("");
    ui->le_updatePhoneNumber->setText("");
    ui->le_updatePassword->setText("");
    ui->le_updateOtherPhoneNumber->setText("");
    ui->tabWidget->setCurrentIndex(0);
}

void UserManager::btnDelOk()
{
    int ret = QMessageBox::question(this,"确认删除","你确定要删除这个用户吗？该操作无法撤销！",QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // ✅ 用户确认删除 → 执行删除语句
        QString deleteSql = "DELETE FROM user_info WHERE user_id = :uid";
        QVariantMap params { {"uid", ui->le_delUserId->text()} };

        bool ok = DatabaseManager::instance()->execTransactional(deleteSql, params);
        if (ok) {
            QMessageBox::information(this, "提示", "删除成功！");
            initShowData();
        } else {
            QMessageBox::warning(this, "错误", "删除失败，请检查数据库连接。");
        }
    }
}

void UserManager::btnDelCancel()
{
    ui->le_delUserId->setText("");
    ui->tabWidget->setCurrentIndex(0);
}

