#ifndef USERMANAGER_H
#define USERMANAGER_H

#include <QObject>
#include "configmanager.h"//配置文件系统
#include "logmanager.h"//日志系统
#include <QMessageBox>
#include <QDialog>

#include <QSqlQuery>
#include <QSqlError>
#include <QDateTime>
#include <QDebug>
#include "databasemanager.h"

namespace Ui {
class UserManagement;
}

class UserManager : public QDialog
{
    Q_OBJECT
public:
    explicit UserManager(QWidget *parent = nullptr);
    ~UserManager();
    void initShowData();
private slots:
    void btnAddUser();
    void btnAddCancel();
    void leUpdateUserId();
    void btnUpdateOk();
    void btnUpdateCancel();
    void btnDelOk();
    void btnDelCancel();

private:
    Ui::UserManagement* ui;
};

#endif // USERMANAGER_H
