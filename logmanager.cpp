#include "logmanager.h"

LogManager *LogManager::instance()
{
    static LogManager* singleton = new LogManager();
    return singleton;
}

LogManager::LogManager(QObject *parent) : QObject(parent), basePath("log")
{
    updatePaths();
}

void LogManager::setBasePath(const QString &path)
{
    basePath = path;
    updatePaths();
}

void LogManager::updatePaths()
{
    QDate date = QDate::currentDate();
    QString monthFolder = date.toString("yyyyMM");
    QString logFileName = date.toString("yyyyMMdd") + ".log";

    currentFolder = basePath + "/" + monthFolder;
    QDir().mkpath(currentFolder);
    logFilePath = currentFolder + "/" + logFileName;
}

QString LogManager::levelToString(LogLevel level)
{
    switch (level) {
    case LogLevel::Debug: return "DEBUG";
    case LogLevel::Info: return "INFO";
    case LogLevel::Warning: return "WARNING";
    case LogLevel::Error: return "ERROR";
    default: return "UNKNOWN";
    }
}

void LogManager::writeLog(const QString &text, LogLevel level)
{
    updatePaths();
    QFile file(logFilePath);
    if (file.open(QIODevice::Append | QIODevice::Text)) {
        QTextStream stream(&file);
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        stream << "[" << timestamp << "] "
               << "[" << levelToString(level) << "] "
               << text << "\n";
        file.close();
    }
}
