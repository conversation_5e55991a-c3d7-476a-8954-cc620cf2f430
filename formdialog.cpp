#include "formdialog.h"
#include "ui_FormDialog.h"

FormDialog::FormDialog(QWidget *parent)
    : QDialog(parent), ui(new Ui::FormDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("报告录参");
    initData();
    connect(ui->btn_saveReportData,&QPushButton::clicked,this,&FormDialog::btn_saveReportData);
    connect(ui->btn_cancel,&QPushButton::clicked,this,&FormDialog::reject);
}

FormDialog::~FormDialog()
{
    delete ui;
}

void FormDialog::initData()
{
    QMap<QString, QLineEdit*> configMap = {
        { "报告标题",ui->le_reportTitle },
        { "编号", ui->le_serialNumber },
        { "委托单位", ui->le_client },
        { "工程名称", ui->le_projectName },
        { "试样名称", ui->le_sampleName },
        { "规格型号", ui->le_specifications },
        { "生产单位", ui->le_productionUnit },
        { "检测类别", ui->le_detectionCategory },
        { "地址", ui->le_address },
        { "邮箱", ui->le_mail },
        { "电话", ui->le_telephone },
        { "传真", ui->le_fax },
        { "邮编", ui->le_postCode },
        { "委托方名称", ui->le_client_name },
        { "委托方地址", ui->le_client_address },
        { "委托方邮政编码", ui->le_client_postalCode },
        { "委托方联系电话", ui->le_client_ContactNumber },
        { "生产方名称", ui->le_manufacturer_name },
        { "生产方地址", ui->le_manufacturer_address },
        { "生产方邮政编码", ui->le_manufacturer_postalCode },
        { "生产方联系电话", ui->le_manufacturer_contactNumber },
        { "型号规格", ui->le_sampleDescription_modelSpecifications },
        { "送检人", ui->le_sampleDescription_submitter },
        { "见证人", ui->le_sampleDescription_witness },
        { "到样日期", ui->le_sampleDescription_sampleArrivalDate },
        { "检验日期", ui->le_sampleDescription_VerificationDate },
        { "报告编制日期", ui->le_reportPreparationDate },
        { "检验项目", ui->le_calibrationItem },
        { "检验依据", ui->le_calibrationBasis },
        { "签发日期", ui->le_inspectionConclusion_issueDate },
        { "型号", ui->le_testProductParameters_model },
        { "出厂编号", ui->le_testProductParameters_factoryNumber },
        { "制造厂家", ui->le_testProductParameters_manufacturer },
        { "出厂日期", ui->le_testProductParameters_factoryDate },
        { "样品描述", ui->le_sampleDescription },
        { "环境温度", ui->le_ambientTemperature },
        { "相对湿度", ui->le_relativeHumidity },
        { "环境气压", ui->le_ambientAirPressure },
        { "试验日期", ui->le_testDate },
        { "仪器编号", ui->le_testInstruments_no },
        { "仪器有效期", ui->le_testInstruments_validityPeriod },
        { "仪器准确级", ui->le_instrumentAccuracy }
    };

    for (auto it = configMap.begin(); it != configMap.end(); ++it) {
        QVariant value = ConfigManager::instance()->get(it.key());
        if (it.value() && !value.isNull()) {
            it.value()->setText(value.toString());
        }
    }

    LogManager::instance()->writeLog("初始化完成，配置数据已回显", LogLevel::Info);
}

bool FormDialog::parameterIsNotEmptyCheck()
{
    QList<QLineEdit*> edits = { ui->le_reportTitle,ui->le_serialNumber,ui->le_client,ui->le_projectName,ui->le_sampleName,
                                ui->le_specifications,ui->le_productionUnit,ui->le_detectionCategory,
                                ui->le_address,ui->le_mail,ui->le_telephone,ui->le_fax,ui->le_postCode,
                                ui->le_client_name,ui->le_client_address,
                                ui->le_client_postalCode,ui->le_client_ContactNumber,ui->le_manufacturer_name,
                                ui->le_manufacturer_address,ui->le_manufacturer_postalCode,
                                ui->le_manufacturer_contactNumber,ui->le_sampleDescription_modelSpecifications,
                                ui->le_sampleDescription_submitter,ui->le_sampleDescription_witness,
                                ui->le_sampleDescription_sampleArrivalDate,
                                ui->le_sampleDescription_VerificationDate,ui->le_reportPreparationDate,
                                ui->le_inspectionConclusion_issueDate,ui->le_testProductParameters_model,
                                ui->le_testProductParameters_factoryNumber,
                                ui->le_testProductParameters_manufacturer,
                                ui->le_testProductParameters_factoryDate,ui->le_sampleDescription,
                                ui->le_ambientTemperature,ui->le_relativeHumidity,
                                ui->le_ambientAirPressure,ui->le_testDate,ui->le_testInstruments_no,
                                ui->le_testInstruments_validityPeriod};

    for (QLineEdit* edit : edits) {
        if (edit->text().trimmed().isEmpty()) {
            QMessageBox::warning(this, "提示", "请填写所有必填项！");
            return false;
        }
    }
    return true;
}

void FormDialog::btn_saveReportData()
{
    bool flag = parameterIsNotEmptyCheck();
    if(!flag){
        return;
    }
    try {
        LogManager::instance()->writeLog("点击了报告录参-保存按钮", LogLevel::Info);
        ConfigManager::instance()->set("报告标题", ui->le_reportTitle->text());
        ConfigManager::instance()->set("编号", ui->le_serialNumber->text());
        ConfigManager::instance()->set("委托单位", ui->le_client->text());
        ConfigManager::instance()->set("工程名称", ui->le_projectName->text());
        ConfigManager::instance()->set("试样名称", ui->le_sampleName->text());
        ConfigManager::instance()->set("规格型号", ui->le_specifications->text());
        ConfigManager::instance()->set("生产单位", ui->le_productionUnit->text());
        ConfigManager::instance()->set("检测类别", ui->le_detectionCategory->text());
        ConfigManager::instance()->set("地址", ui->le_address->text());
        ConfigManager::instance()->set("邮箱", ui->le_mail->text());
        ConfigManager::instance()->set("电话", ui->le_telephone->text());
        ConfigManager::instance()->set("传真", ui->le_fax->text());
        ConfigManager::instance()->set("邮编", ui->le_postCode->text());

        ConfigManager::instance()->set("试样名称1", ui->le_sampleName->text());
        ConfigManager::instance()->set("检测类别1", ui->le_detectionCategory->text());
        ConfigManager::instance()->set("报告编号", ui->le_serialNumber->text());
        ConfigManager::instance()->set("报告名1", ui->le_reportTitle->text());
        ConfigManager::instance()->set("报告名2", ui->le_reportTitle->text());
        ConfigManager::instance()->set("报告名3", ui->le_reportTitle->text());

        ConfigManager::instance()->set("委托方名称", ui->le_client_name->text());
        ConfigManager::instance()->set("委托方地址", ui->le_client_address->text());
        ConfigManager::instance()->set("委托方邮政编码", ui->le_client_postalCode->text());
        ConfigManager::instance()->set("委托方联系电话", ui->le_client_ContactNumber->text());
        ConfigManager::instance()->set("生产方姓名", ui->le_manufacturer_name->text());
        ConfigManager::instance()->set("生产方地址", ui->le_manufacturer_address->text());
        ConfigManager::instance()->set("生产方邮政编码", ui->le_manufacturer_postalCode->text());
        ConfigManager::instance()->set("生产方联系电话", ui->le_manufacturer_contactNumber->text());
        ConfigManager::instance()->set("型号规格", ui->le_sampleDescription_modelSpecifications->text());
        ConfigManager::instance()->set("送检人", ui->le_sampleDescription_submitter->text());
        ConfigManager::instance()->set("见证人", ui->le_sampleDescription_witness->text());
        ConfigManager::instance()->set("到样日期", ui->le_sampleDescription_sampleArrivalDate->text());
        ConfigManager::instance()->set("检验日期", ui->le_sampleDescription_VerificationDate->text());
        ConfigManager::instance()->set("报告编制日期", ui->le_reportPreparationDate->text());
        ConfigManager::instance()->set("检验项目", ui->le_calibrationItem->text());
        ConfigManager::instance()->set("检验依据", ui->le_calibrationBasis->text());
        ConfigManager::instance()->set("签发日期", ui->le_inspectionConclusion_issueDate->text());
        ConfigManager::instance()->set("型号", ui->le_testProductParameters_model->text());
        ConfigManager::instance()->set("出厂编号", ui->le_testProductParameters_factoryNumber->text());
        ConfigManager::instance()->set("制造厂家", ui->le_testProductParameters_manufacturer->text());
        ConfigManager::instance()->set("出厂日期", ui->le_testProductParameters_factoryDate->text());
        ConfigManager::instance()->set("样品描述", ui->le_sampleDescription->text());
        ConfigManager::instance()->set("环境温度", ui->le_ambientTemperature->text());
        ConfigManager::instance()->set("相对湿度", ui->le_relativeHumidity->text());
        ConfigManager::instance()->set("环境气压", ui->le_ambientAirPressure->text());
        ConfigManager::instance()->set("试验日期", ui->le_testDate->text());
        ConfigManager::instance()->set("仪器编号", ui->le_testInstruments_no->text());
        ConfigManager::instance()->set("仪器有效期", ui->le_testInstruments_validityPeriod->text());
        ConfigManager::instance()->set("仪器准确级", ui->le_instrumentAccuracy->text());
        ConfigManager::instance()->save();
        LogManager::instance()->writeLog("配置文件对应数据成功保存", LogLevel::Info);
        accept();
    } catch (const std::exception &e) {
        LogManager::instance()->writeLog("配置文件数据保存失败!", LogLevel::Error);
        LogManager::instance()->writeLog(e.what(), LogLevel::Error);
    }


}
