#include "curvedisplay.h"

CurveDisplay::CurveDisplay(QWidget *parent) : QWidget(parent)
{
    setMinimumSize(400, 400);
}

void CurveDisplay::addDataPoint(double value)
{
    if (m_data.size() >= maxPoints)
        m_data.pop_front(); // 移除最旧的数据

    m_data.push_back(value);
    update(); // 触发绘图
}

void CurveDisplay::clearData()
{
    m_data.clear();
    update();
}

void CurveDisplay::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制背景
    painter.fillRect(rect(), Qt::white);

    // 坐标系边距
    const int margin = 40;
    QRectF drawArea = QRectF(margin, margin, width() - 2 * margin, height() - 2 * margin);

    // 绘制坐标轴
    painter.setPen(QPen(Qt::black, 1));
    painter.drawRect(drawArea);

    // 绘制刻度线（可扩展为动态刻度）
    for (int i = 0; i <= maxPoints; ++i) {
        int x = drawArea.left() + (drawArea.width() / maxPoints) * i;
        painter.drawLine(x, drawArea.bottom(), x, drawArea.bottom() + 5);
    }

    for (int i = 0; i <= 11; ++i) {
        int y = drawArea.bottom() - (drawArea.height() / 11) * i;
        painter.drawLine(drawArea.left() - 5, y, drawArea.left(), y);
    }

    // 绘制曲线
    if (m_data.size() > 1) {
        painter.setPen(QPen(Qt::blue, 2));
        for (int i = 0; i < m_data.size() - 1; ++i) {
            double x1 = drawArea.left() + (drawArea.width() / maxPoints) * i;
            double y1 = drawArea.bottom() - (m_data[i] / maxForce) * drawArea.height();

            double x2 = drawArea.left() + (drawArea.width() / maxPoints) * (i + 1);
            double y2 = drawArea.bottom() - (m_data[i + 1] / maxForce) * drawArea.height();

            painter.drawLine(QPointF(x1, y1), QPointF(x2, y2));
        }
    }
}
