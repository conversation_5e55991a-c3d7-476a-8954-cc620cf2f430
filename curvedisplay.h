#ifndef CURVEDISPLAY_H
#define CURVEDISPLAY_H

#include <QWidget>
#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QVector>

class CurveDisplay : public QWidget
{
    Q_OBJECT
public:
    explicit CurveDisplay(QWidget *parent = nullptr);
    void addDataPoint(double value);
    void clearData();
protected:
    void paintEvent(QPaintEvent *event) override;
private:
    QVector<double> m_data;
    const double maxPoints = 30.0;
    const double maxForce = 110.0;
signals:

};

#endif // CURVEDISPLAY_H
