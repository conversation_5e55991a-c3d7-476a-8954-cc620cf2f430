#ifndef MODBUSMANAGER_H
#define MODBUSMANAGER_H

#include <QObject>
#include <QModbusTcpClient>
#include <QModbusReply>
#include <QModbusDataUnit>
#include <QModbusDevice>

#include <QTimer>
#include <QVariantMap>
#include <functional>
#include "configmanager.h"
#include "logmanager.h"
#include "databasemanager.h"

#include <QSqlQuery>
#include <QSqlError>
#include <QDateTime>
#include <QDebug>
#include <cmath>

class ModbusManager : public QObject
{
    Q_OBJECT
public:
    static ModbusManager* instance();//单例访问接口

private:
    explicit ModbusManager(QObject *parent = nullptr);
    ModbusManager(const ModbusManager&) = delete;
    ModbusManager& operator=(const ModbusManager&) = delete;
public:
    void init();
    void writeRegisterValue(int address, quint16 value);//写数据
    bool isConnected() const;
    void readVoltageValueAndReturnSignal();//读电压值并返回信号
    void readTheTensionValueAndReturnTheSignal();//读拉力值并返回信号
    void readTheDisplacementValueAndReturnTheSignal();//读位移值并返回信号
    void writeStartHighVoltageSignal(int address, quint16 value);//发送启动高压信号
    void writeCloseHighVoltageSignal(int address, quint16 value);//发送关闭高压信号
    void writeLoadSignal(int address, quint16 value);//发送加载信号
    void writeUnistallSignal(int address, quint16 value);//发送卸载信号
    void writeResetSignal(int address, quint16 value);//发送复位信号
    void writeStopSignal(int address, quint16 value);//发送暂停信号
    void writeContinueSignal(int address, quint16 value);//发送继续信号
    void writeStartExperimenting(int address, quint16 value);//发送开始试验信号
    void writeStopExperimenting(int address, quint16 value);//发送停止试验信号
    bool plcConnectFlag;
private slots:
    void plcConnectState(QModbusDevice::State state);//plc连接状态
    void plcConnectError(QModbusDevice::Error error);//plc连接异常操作
    void checkHeartbeat();  // 心跳包读取逻辑
signals:
    void voltageValueDataInsertion(const QDateTime& timestamp, double tensionValue);//返回电压值
    void tensionDataInserted(const QDateTime& timestamp, double tensionValue);//返回拉力值
    void displacementDataInsertion(double tensionValue);//返回位移值

    //    displacementDataInsertion
    void disconnectedFromPLC();//状态栏信号
    void connedtedFromPLC();//状态栏信号
private:
    QModbusTcpClient* modbusClient;
    QString plc_ip;
    int plc_port;
    QTimer* connectTimer;//初始化连接定时器
    QTimer* conditionMonitoring;//状态监测定时器（心跳包定时器）

    int heartbeatFailureCount = 0;  // 心跳失败次数计数器
    const int heartbeatFailureThreshold = 3; // 触发重连的阈值

};

#endif // MODBUSMANAGER_H
