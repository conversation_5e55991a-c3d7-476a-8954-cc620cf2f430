#include "databasemanager.h"

DatabaseManager* DatabaseManager::instance()
{
    static DatabaseManager* singleton = new DatabaseManager();
    return singleton;
}

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent) {}

bool DatabaseManager::init(const QString& dbPath)
{
    try {
        LogManager::instance()->writeLog("数据库表开始进行初始化", LogLevel::Info);

        QDir().mkpath(QFileInfo(dbPath).absolutePath());
        db = QSqlDatabase::addDatabase("QSQLITE");
        db.setDatabaseName(dbPath);

        if (!db.open()) {
            LogManager::instance()->writeLog("数据库打开失败：" + db.lastError().text(), LogLevel::Error);
            return false;
        }

        // 建表语句（如果不存在）
        QSqlQuery query;
        QString createTableSql = R"(
                                 CREATE TABLE IF NOT EXISTS TestRecord (
                                 id INTEGER PRIMARY KEY,  -- 自增主键
                                 reportId TEXT,                         -- 报告ID（用于归档）
                                 timestamp TEXT,                        -- 记录时间
                                 operator TEXT,                         -- 操作员
                                 testMethod TEXT,                       -- 试验方式
                                 sfl TEXT,                               -- SFL参数
                                 holdTime REAL,                         -- 保持时间（秒）
                                 force REAL,                             -- 拉力值（N）
                                 voltage REAL,                           -- 电压值（V）
                                 displacement REAL,                      -- 位移值（mm）
                                 result TEXT                             -- 检测结果（合格/不合格等）
                                 )
                                 )";

        if (!query.exec(createTableSql)) {
            LogManager::instance()->writeLog("TestRecord表创建失败！" + db.lastError().text(), LogLevel::Error);
            QString lastErrorMsg = query.lastError().text();
            LogManager::instance()->writeLog("TestRecord表创建失败原因：" + lastErrorMsg, LogLevel::Error);
            return false;
        }

        //拉力测量记录表
        QString createTensionTableSql = R"(
                                        CREATE TABLE IF NOT EXISTS tension_measurement (
                                        trace_id TEXT,
                                        timestamp TEXT,
                                        tension_value REAL
                                        )
                                        )";
        if (!query.exec(createTensionTableSql)) {
            LogManager::instance()->writeLog("tension_measurement表创建失败！" + db.lastError().text(), LogLevel::Error);
            QString lastErrorMsg = query.lastError().text();
            LogManager::instance()->writeLog("tension_measurement表创建失败原因：" + lastErrorMsg, LogLevel::Error);
            return false;
        }

        //位移测量记录表
        QString createDisplacementTableSql = R"(
                                             CREATE TABLE IF NOT EXISTS displacement_measurement (
                                             trace_id TEXT,
                                             timestamp TEXT,
                                             displacement_value REAL
                                             )
                                             )";
        if (!query.exec(createDisplacementTableSql)) {
            LogManager::instance()->writeLog("displacement_measurement表创建失败！" + db.lastError().text(), LogLevel::Error);
            QString lastErrorMsg = query.lastError().text();
            LogManager::instance()->writeLog("displacement_measurement表创建失败原因：" + lastErrorMsg, LogLevel::Error);
            return false;
        }

        //电压测量记录表
        QString createVoltageTableSql = R"(
                                        CREATE TABLE IF NOT EXISTS voltage_measurement (
                                        trace_id TEXT,
                                        timestamp TEXT,
                                        voltage_value REAL
                                        )
                                        )";

        if (!query.exec(createVoltageTableSql)) {
            LogManager::instance()->writeLog("voltage_measurement表创建失败！" + db.lastError().text(), LogLevel::Error);
            QString lastErrorMsg = query.lastError().text();
            LogManager::instance()->writeLog("voltage_measurement表创建失败原因：" + lastErrorMsg, LogLevel::Error);
            return false;
        }

        //用户管理表
        QString createUserTableSql = R"(
                                     CREATE TABLE IF NOT EXISTS user_info (
                                     user_id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 自增主键：用户编号
                                     username TEXT NOT NULL,                     -- 用户名
                                     password TEXT NOT NULL,                     -- 密码（建议加密存储）
                                     contact TEXT,                                -- 联系方式（电话或微信）
                                     home_address TEXT,                           -- 家庭住址
                                     emergency_contact TEXT,                      -- 紧急联系人电话
                                     role TEXT DEFAULT 'user'                    -- 用户角色：admin / user 等
                                     )
                                     )";
        if (!query.exec(createUserTableSql)) {
            LogManager::instance()->writeLog("user_info表创建失败！" + db.lastError().text(), LogLevel::Error);
            QString errorMsg = query.lastError().text();
            LogManager::instance()->writeLog("user_info表创建失败原因：" + errorMsg, LogLevel::Error);
        }else{
            QSqlQuery checkQuery;
            checkQuery.prepare("SELECT COUNT(*) FROM user_info WHERE username = :uname");
            checkQuery.bindValue(":uname", "admin");
            if (checkQuery.exec() && checkQuery.next()) {
                int count = checkQuery.value(0).toInt();
                if (count == 0) {
                    // ✅ 仅在 admin 不存在时插入初始数据
                    QString insertSql = R"(
                                        INSERT INTO user_info (username, password, contact, home_address, emergency_contact, role)
                                        VALUES ('admin', '123456', '', '', '', 'admin')
                                        )";
                    if (!query.exec(insertSql)) {
                        QString err = query.lastError().text();
                        LogManager::instance()->writeLog("插入初始管理员失败：" + err, LogLevel::Error);
                    }
                }
            }
        }
        return true;
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("数据库初始化过程中捕获异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        return false;
    }catch (...) {
        LogManager::instance()->writeLog("数据库初始化过程中发生未知异常", LogLevel::Error);
        return false;
    }


}

bool DatabaseManager::execTransactional(const QString &sql, const QVariantMap &params)
{
    try {
        if (!db.isOpen()){
            LogManager::instance()->writeLog("数据库未打开，无法执行事务", LogLevel::Error);
            return false;
        }

        QSqlQuery query;
        query.prepare(sql);

        LogManager::instance()->writeLog("准备执行事务 SQL：" + sql, LogLevel::Debug);

        for (const QString& key : params.keys()){
            query.bindValue(":" + key, params.value(key));
        }

        if(!db.transaction()){
            LogManager::instance()->writeLog("事务开始失败：" + db.lastError().text(), LogLevel::Error);
            return false;
        }

        if (!query.exec()) {
            db.rollback();
            QString str = query.lastError().text();
            LogManager::instance()->writeLog("SQL 执行失败并已回滚：" + str, LogLevel::Error);
            return false;
        }

        if(!db.commit()){
            LogManager::instance()->writeLog("事务提交失败：" + db.lastError().text(), LogLevel::Error);
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("事务执行过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        return false;
    }catch (...) {
        LogManager::instance()->writeLog("事务执行过程中发生未知异常", LogLevel::Error);
        return false;
    }

}

QSqlQuery DatabaseManager::executeQuery(const QString &sql, const QVariantMap &params)
{
    QSqlQuery query;
    try {
        query.prepare(sql);

        for (const QString& key : params.keys()){
            query.bindValue(":" + key, params.value(key));
        }

        if (!query.exec()) {
            QString str = query.lastError().text();
            LogManager::instance()->writeLog("SQL 执行失败：" + str, LogLevel::Error);
        }else{
            LogManager::instance()->writeLog("SQL 执行成功：" + sql, LogLevel::Debug);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("SQL 查询异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    }catch (...) {
        LogManager::instance()->writeLog("SQL 查询过程中发生未知异常", LogLevel::Error);
    }
    return query;

}

QString DatabaseManager::lastError()
{
    return db.lastError().text();
}
