#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QObject>
#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QFont>

#include <QSqlQuery>
#include <QSqlError>

#include "configmanager.h"

class LoginDialog : public QDialog
{
    Q_OBJECT
public:
    explicit LoginDialog(QWidget *parent = nullptr);
private slots:
    void handleLogin();
private:
    QLabel* labelUser;
    QLineEdit* usernameEdit;

    QLabel* labelPass;
    QLineEdit *passwordEdit;

    QPushButton *loginButton;
    QPushButton *cancelButton;
};

#endif // LOGINDIALOG_H
