#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <iostream>

#include "logmanager.h"//日志系统
#include "configmanager.h"//配置文件系统
#include "databasemanager.h"//数据库管理系统
#include "graphictextbutton.h"//这里是自定义了按钮，控件中全部都提升为自定义按钮
#include "formdialog.h"//这个是报告参数填写的弹出对话框
#include "modbusmanager.h" //这个是plc通讯模块管理系统
#include "usermanager.h" //这个是用户管理系统

//下面是关于曲线图的头文件
#include <QtCharts/QChartView>
#include <QtCharts/QChart>
#include <QtCharts/QSplineSeries>
#include <QtCharts/QValueAxis>
#include <QtCharts/QLineSeries>
using namespace QtCharts;

#include <QMessageBox>
#include <QMap>
#include <QFile>
#include <QByteArray>
#include <QJsonParseError>
#include <QJsonDocument>
#include <QJsonObject>
#include <QAxObject>
#include <QFileDialog>
#include <QThread>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    void init();//整体初始化函数
    void initFrontEndPageDisplay();//前端界面展示
    void initButtonSlotSignals();//初始化按钮信号槽
    void initData();//数据、配置文件初始化
    void initConfigManagerData();//配置文件管理系统初始化
    void initSqlManagerData();//数据库管理系统初始化
    void upperPart();//上边功能按钮展示
    void leftDisplay();//左边展示
    void rightDisplay();//右边展示
    void voltageTimeGraphDisplay();//电压时间曲线图展示
    void tensionTimeCurveDisplay();//拉力时间曲线图展示
    void downTableDisplay();//下边表格展示
private:
    //这个函数是把json配置文件中的每一项保存到容器中
    QMap<QString,QString> loadBookmarkDataFromJson(const QString& jsonFilePath);
    //这个函数是把容器中的值对应到word文档上的书签上进行替换
    void fillWordBookmarks(const QString& templatePath, const QString& savePath, const QMap<QString, QString>& bookmarkValues);
    //为避免按钮重复的定义，现在用一个函数表示
    void initControlButton(GraphicTextButton* button, const QString& imagePath, const QString& text, const QSize& size = QSize(80, 80));
    //这里是试验停止后，数据插入到tablewidget控件中
    void updateTableWidget(const QVariantMap& params);
    //将试验数据插入到配置标签文件中
    void insertTestData();
private slots:
    void btn_startHighVoltageSlots();//启动高压
    void btn_closeHighVoltageSlots();//关闭高压
    void btn_increasedPressureSlots();//加载
    void btn_reduceStressSlots();//卸载
    void btn_resetSlots();//复位
    void btn_pauseSlots();//暂停
    void btn_oneClickResetSlots();//一键清零
    void btn_startTheExperimentSlots();//开始试验
    void btn_stopTheExperimentSlots();//停止试验
    void btn_reportReferencesSlots();//报告录参
    void btn_generateReportSlots();//生成报告
    void btn_printingReportsSlots();//打印报告
    void btn_userManagementSlots();//用户管理
    void btn_logOutSlots();//退出登录

    void trialStart_upDataDisplay();//试验启动定时器启动，获取数据
    void voltageValueConversionCurve(const QDateTime& timestamp, double displacement);//电压值显示到曲线图上
    void tensionValueToCurveGraph(const QDateTime& timestamp, double tension);//拉力值显示到曲线图上
    void displacementValueDataInsertion(double tension);

    void onTableRowSelected(); //方便历史数据回溯，点击某一行之后，会触发函数，函数处理的是触发的ID，进行数据查询并将结果反置于曲线图上

private:
    Ui::MainWindow *ui;
    QLineSeries *seriesKVTime;//电压时间曲线图对象
    QLineSeries *seriesKNTime;//拉力时间曲线图对象
    QTimer* startTheExperiment;//开始试验按钮定时器
    QDateTime startTime;//获取系统当前时间，为了后续实现时间戳除以1000换算到曲线图上
    int testCount = 0;//试验次数
};
#endif // MAINWINDOW_H
