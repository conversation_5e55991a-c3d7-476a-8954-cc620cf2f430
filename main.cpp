#include "mainwindow.h"

#include <QApplication>
#include "logindialog.h"
#include <QNetworkProxy>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    a.setWindowIcon(QIcon("images/title.ico"));
    //设置全局网络代理
    QNetworkProxy proxy;
    proxy.setType(QNetworkProxy::NoProxy); // 如果需要代理，请设置为适当的代理类型
    QNetworkProxy::setApplicationProxy(proxy);

    MainWindow w;
    w.setWindowState(Qt::WindowMaximized);  // 设置窗口最大化
    // 弹出登录界面
    LoginDialog login;
    if (login.exec() == QDialog::Accepted) {
        w.show();
        return a.exec();
    } else {
        return 0; // 用户取消登录，退出程序
    }
}
