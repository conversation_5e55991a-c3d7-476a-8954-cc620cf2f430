#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

/*
验证数据库管理系统功能
connect(ui->pushButton_3,&QPushButton::clicked,this,[=](){

    //增加
    QString sql1 = "INSERT INTO TestRecord (timestamp, operator, force, result) VALUES (:t, :o, :f, :r)";
    QVariantMap params1 {
        {"t", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")},
        {"o", "东文"},
        {"f", 296.6},
        {"r", "通过"}
    };
    DatabaseManager::instance()->execTransactional(sql1, params1);
    //删除
    QString sql2 = "DELETE FROM TestRecord WHERE id IN (:id1, :id2)";
    QVariantMap params2 {
        {"id1", 2},
        {"id2", 5}
    };
    DatabaseManager::instance()->execTransactional(sql2, params2);
    //修改
    QString sql3 = "UPDATE TestRecord SET result = :r WHERE id = :id";
    QVariantMap params3 {
        {"r", "失败"},
        {"id", 1}
    };
    DatabaseManager::instance()->execTransactional(sql3, params3);
    //查询
    QString sql = "SELECT * FROM TestRecord WHERE force > :forceLimit AND result = :status";
    QVariantMap params {
        {"forceLimit", 290.0},
        {"status", "通过"}
    };
    auto query = DatabaseManager::instance()->executeQuery(sql, params);
    while (query.next()) {
        qDebug() << query.value("id").toInt()
                 << query.value("force").toDouble()
                 << query.value("timestamp").toString();
    }
});
*/

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QVariantMap>
#include <QDebug>
#include <QSqlRecord>
#include <QDir>

#include "logmanager.h"

class DatabaseManager : public QObject
{
    Q_OBJECT
public:
    static DatabaseManager* instance();
    bool init(const QString& dbPath = "data/data.db");
    bool execTransactional(const QString& sql,const QVariantMap& params);
    QSqlQuery executeQuery(const QString& sql, const QVariantMap& params);
    QString lastError();
private:
    explicit DatabaseManager(QObject *parent = nullptr);
    DatabaseManager(const DatabaseManager&) = delete;
    DatabaseManager& operator=(const DatabaseManager&) = delete;

    QSqlDatabase db;

signals:

};

#endif // DATABASEMANAGER_H
