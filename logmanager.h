#ifndef LOGMANAGER_H
#define LOGMANAGER_H
/*
LogManager::instance()->writeLog("验证提示日志", LogLevel::Info);
LogManager::instance()->writeLog("验证警告日志", LogLevel::Warning);
LogManager::instance()->writeLog("验证错误日志", LogLevel::Error);
LogManager::instance()->writeLog("验证其他状态", LogLevel::Debug);
*/
#include <QObject>
#include <QString>
#include <QFile>
#include <QDateTime>
#include <QDir>
#include <QTextStream>

//日志等级枚举
enum class LogLevel{
    Debug,
    Info,
    Warning,
    Error
};

class LogManager : public QObject
{
    Q_OBJECT
public:
    static LogManager* instance();//获取单例
    void writeLog(const QString& text,LogLevel level = LogLevel::Info);
    void setBasePath(const QString& path);//可修改根路径
private:
    explicit LogManager(QObject* parent = nullptr);
    LogManager(const LogManager&) = delete;
    LogManager& operator=(const LogManager) = delete ;

    QString basePath;//根目录路径
    QString currentFolder;//当前年月目录路径
    QString logFilePath;//日志文件完整路径

    void updatePaths();//根据当前时间更新路径
    QString levelToString(LogLevel level);//转换等级为文本
signals:

};

#endif // LOGMANAGER_H
