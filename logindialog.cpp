#include "logindialog.h"

LoginDialog::LoginDialog(QWidget *parent) : QDialog(parent)
{
    // --- 1. 窗口级别的基本设置 ---
    this->setWindowTitle("用户登录");
    this->setFixedSize(400, 320); // 设置固定宽高
    this->setFont(QFont("Microsoft YaHei", 14)); // 字体 + 大小

    // --- 2. "账号"功能区的控件创建与配置 ---
    labelUser = new QLabel("  账 号 : ",this);
    labelUser->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    labelUser->setFont(QFont("Microsoft YaHei", 14));

    usernameEdit = new QLineEdit(this);
    usernameEdit->setPlaceholderText("请输入账号");
    usernameEdit->setMinimumHeight(32);
    usernameEdit->setFont(QFont("Microsoft YaHei", 14));

    // --- 3. "密码"功能区的控件创建与配置 ---
    labelPass = new QLabel("  密 码 : ",this);
    labelPass->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

    passwordEdit = new QLineEdit(this);
    passwordEdit->setPlaceholderText("请输入密码");
    passwordEdit->setEchoMode(QLineEdit::Password);
    passwordEdit->setMinimumHeight(32);

    // --- 4. "按钮"功能区的控件创建 ---
    loginButton = new QPushButton("登 录", this);
    loginButton->setFixedSize(80, 36);

    cancelButton = new QPushButton("取 消", this);
    cancelButton->setFixedSize(80, 36);

    // --- 5. 布局管理：使用布局管理器智能地排列控件 ---
    QHBoxLayout *buttonLayout = new QHBoxLayout(); // 不传递父对象
    buttonLayout->addStretch();
    buttonLayout->addWidget(loginButton);
    buttonLayout->addWidget(cancelButton);
    buttonLayout->addStretch();

    // 主布局
    QGridLayout *grid = new QGridLayout(this); // 只有主布局设置父对象
    grid->addWidget(labelUser, 0, 0);
    grid->addWidget(usernameEdit, 0, 1);
    grid->addWidget(labelPass, 1, 0);
    grid->addWidget(passwordEdit, 1, 1);
    grid->addLayout(buttonLayout, 2, 0, 1, 2); // 跨两列

    // --- 6. 信号槽连接 ---
    connect(loginButton, &QPushButton::clicked, this, &LoginDialog::handleLogin);
    connect(cancelButton, &QPushButton::clicked, this, &LoginDialog::reject);
}

//在登录这里还要加上时间限制，以方便尾款收回，但是需要在配置文件中可以进行管理
void LoginDialog::handleLogin()
{
    try {
        QString configTimeStr = ConfigManager::instance()->get("login_Time").toString(); // 例如 "2025-08-20"
        qDebug()<<"ddd:"<<configTimeStr;
        QDate configDate = QDate::fromString(configTimeStr, "yyyy-MM-dd");
        QDate currentDate = QDate::currentDate();

        if (currentDate > configDate) {
            LogManager::instance()->writeLog("当前时间已超过配置允许时间，操作被阻止", LogLevel::Warning);
            QMessageBox::warning(this, "登录失败", "当前时间已超过配置允许时间，操作被阻止,请联系售后人员！");
            return;
        }

        QString username = usernameEdit->text().trimmed();
        QString password = passwordEdit->text().trimmed();
        QSqlQuery query;
        query.prepare(R"(SELECT role FROM user_info WHERE username = :username AND password = :password LIMIT 1)");
        query.bindValue(":username", username);
        query.bindValue(":password", password);  // 如果用明文也可以直接传 password

        if (query.exec() && query.next()) {
            QString role = query.value("role").toString();
            ConfigManager::instance()->set("userPermissions", role);
            bool flag = ConfigManager::instance()->save();
            if (!flag) {
                LogManager::instance()->writeLog("用户权限配置保存失败", LogLevel::Warning);
            } else {
                LogManager::instance()->writeLog("用户权限配置保存成功，角色：" + role, LogLevel::Info);
            }
            accept();  // 登录成功
        } else{
            LogManager::instance()->writeLog("登录失败：用户名或密码不匹配", LogLevel::Warning);
            QMessageBox::warning(this, "登录失败", "用户名或密码错误！");
            usernameEdit->setText("");
            passwordEdit->setText("");
        }
    }  catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("登录过程发生异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("登录过程发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "发生未知异常，请查看日志");
    }

}
