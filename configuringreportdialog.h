#ifndef CONFIGURINGREPORTDIALOG_H
#define CONFIGURINGREPORTDIALOG_H

#include <QObject>
#include <QDialog>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QPushButton>
#include <QStringList>
#include <QHBoxLayout>
#include <QLabel>
#include <QSettings>
#include <QDesktopWidget>
#include <QApplication>
#include <QMessageBox>

class ConfiguringReportDialog : public QDialog
{
    Q_OBJECT
public:
    explicit ConfiguringReportDialog(const QStringList &fieldNames, QWidget *parent = nullptr);
    QMap<QString, QString> getConfigValues() const;
private slots:
    void onSaveClicked();
    void onCancelClicked();
private:
    void setupLayout();
    void centerDialog();

    QList<QLineEdit*> m_lineEdits;
    QStringList m_fieldNames;
    QVBoxLayout *mainLayout;
    QPushButton *btnSave;
    QPushButton *btnCancel;
signals:

};

#endif // CONFIGURINGREPORTDIALOG_H
