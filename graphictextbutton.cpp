#include "graphictextbutton.h"

GraphicTextButton::GraphicTextButton(QWidget *parent)
    : QPushButton(parent), m_textFont(font()) {
    setFlat(true);
    setCursor(Qt::PointingHandCursor);
}

void GraphicTextButton::setGraphic(const QPixmap &pixmap) {
    m_icon = pixmap;
    update();
}

void GraphicTextButton::setLabel(const QString &text) {
    m_text = text;
    update();
}

QString GraphicTextButton::getLabel() const
{
    return m_text;
}

void GraphicTextButton::setIconRatio(double ratio) {
    m_iconRatio = qBound(0.0, ratio, 1.0);
    update();
}

void GraphicTextButton::setSpacing(int spacing) {
    m_spacing = spacing;
    update();
}

void GraphicTextButton::setTextStyle(const QFont &font, const QColor &color) {
    m_textFont = font;
    m_textColor = color;
    update();
}

void GraphicTextButton::setCornerRadius(int radius) {
    m_cornerRadius = radius;
    update();
}

void GraphicTextButton::enterEvent(QEvent *event) {
    m_hovered = true;
    update();
    QPushButton::enterEvent(event);
}

void GraphicTextButton::leaveEvent(QEvent *event) {
    m_hovered = false;
    update();
    QPushButton::leaveEvent(event);
}

void GraphicTextButton::mousePressEvent(QMouseEvent *event) {
    m_pressed = true;
    update();
    QPushButton::mousePressEvent(event);
}

void GraphicTextButton::mouseReleaseEvent(QMouseEvent *event) {
    m_pressed = false;
    update();
    QPushButton::mouseReleaseEvent(event);
}

void GraphicTextButton::paintEvent(QPaintEvent *event) {
    Q_UNUSED(event);
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    QColor bgColor = Qt::white;
    if (!isEnabled())           bgColor = Qt::lightGray;
    else if (m_pressed)         bgColor = QColor("#CCE5FF");
    else if (m_hovered)         bgColor = QColor("#E6F2FF");
    painter.setBrush(bgColor);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(rect(), m_cornerRadius, m_cornerRadius);

    int w = width(), h = height();
    int iconH = static_cast<int>(h * m_iconRatio);
    QRect iconRect(0, 0, w, iconH);
    QRect textRect(0, iconH + m_spacing, w, h - iconH - m_spacing);

    if (!m_icon.isNull()) {

        QPixmap scaledIcon = m_icon.scaled(iconRect.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);

        QPixmap roundedPixmap(scaledIcon.size());
        roundedPixmap.fill(Qt::transparent);

        QPainter maskPainter(&roundedPixmap);
        maskPainter.setRenderHint(QPainter::Antialiasing);
        QPainterPath path;
        path.addRoundedRect(roundedPixmap.rect(), m_cornerRadius, m_cornerRadius);
        maskPainter.setClipPath(path);
        maskPainter.drawPixmap(0, 0, scaledIcon);

        // 计算居中位置，避免图像偏移
        int xOffset = iconRect.x() + (iconRect.width() - roundedPixmap.width()) / 2;
        int yOffset = iconRect.y() + (iconRect.height() - roundedPixmap.height()) / 2;
        painter.drawPixmap(QPoint(xOffset, yOffset), roundedPixmap);
    }
    painter.setFont(m_textFont);
    painter.setPen(isEnabled() ? m_textColor : Qt::gray);
    painter.drawText(textRect, Qt::AlignCenter, m_text);


}
