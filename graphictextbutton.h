#ifndef GRAPHICTEXTBUTTON_H
#define GRAPHICTEXTBUTTON_H
/*
    ui->pushButton_5->setIconRatio(0.6);              // 图像占 70%
    ui->pushButton_5->setSpacing(2);                  // 图文间距 2px
    ui->pushButton_5->setTextStyle(font, Qt::black);  // 文本样式设置
    ui->pushButton_5->setCornerRadius(10);
    ui->pushButton_5->setMinimumSize(80, 80);  // 或者 100x120 等
*/
#include <QPushButton>
#include <QPixmap>
#include <QString>
#include <QFont>
#include <QColor>
#include <QPainter>
#include <QStyleOption>

class GraphicTextButton : public QPushButton
{
    Q_OBJECT
public:
    explicit GraphicTextButton(QWidget *parent = nullptr);

    void setGraphic(const QPixmap &pixmap);
    void setLabel(const QString &text);
    QString getLabel() const;
    void setIconRatio(double ratio=0.75);                      // 图像高度比例 0.0 ~ 1.0
    void setSpacing(int spacing=2);                         // 图像与文字间距 px
    void setTextStyle(const QFont &font = QFont(QFont("", 12, QFont::Bold)), const QColor &color = Qt::black);// 文本样式设置
    void setCornerRadius(int radius=10);                     // 设置圆角半径 px
protected:
    void paintEvent(QPaintEvent *event) override;
    void enterEvent(QEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    QPixmap m_icon;
    QString m_text;
    double m_iconRatio = 0.6;
    int m_spacing = 4;
    QFont m_textFont;
    QColor m_textColor = Qt::black;
    int m_cornerRadius = 8;

    bool m_hovered = false;
    bool m_pressed = false;
};

#endif // GRAPHICTEXTBUTTON_H
