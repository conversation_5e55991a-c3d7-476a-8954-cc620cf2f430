#include "configmanager.h"

ConfigManager* ConfigManager::instance()
{
    static ConfigManager* singleton = new ConfigManager();
    return singleton;
}

ConfigManager::ConfigManager()
    :configFilePath("config/action.json")
{
    QDir().mkpath(QFileInfo(configFilePath).absolutePath()); // 创建 config 文件夹
    qDebug() << "Config path:" << configFilePath;
    qDebug() << "Exists:" << QFile(configFilePath).exists();
}

bool ConfigManager::load()
{
    try {
        QFile file(configFilePath);
        if (!file.exists()) {
            qDebug() << "配置文件不存在：" << configFilePath;
            return false;
        }
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            qDebug() << "无法打开配置文件：" << file.errorString();
            return false;
        }

        QByteArray data = file.readAll();
        file.close();

        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(data,&error);
        if (error.error != QJsonParseError::NoError) {
            qDebug() << "JSON 格式解析失败：" << error.errorString();
            return false;
        }
        configObject = doc.object();
        return true;

    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("加载配置文件时发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        return false;
    } catch (...) {
        LogManager::instance()->writeLog("加载配置文件时发生未知异常", LogLevel::Error);
        return false;
    }

}

bool ConfigManager::save()
{
    try {
        QFile file(configFilePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            LogManager::instance()->writeLog("无法打开配置文件写入：" + file.errorString(), LogLevel::Error);
            return false;
        }

        QJsonDocument doc(configObject);
        qint64 writtenBytes = file.write(doc.toJson(QJsonDocument::Indented));
        file.close();

        if (writtenBytes <= 0) {
            LogManager::instance()->writeLog("配置内容写入失败：" + configFilePath, LogLevel::Warning);
            return false;
        }

        LogManager::instance()->writeLog("配置保存成功：" + configFilePath, LogLevel::Info);
        return true;

    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("保存配置文件时发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        return false;
    } catch (...) {
        LogManager::instance()->writeLog("保存配置文件时发生未知异常", LogLevel::Error);
        return false;
    }
}

QVariant ConfigManager::get(const QString& key)
{
    return configObject.value(key).toVariant();
}

void ConfigManager::set(const QString& key, const QVariant& value)
{
    configObject.insert(key, QJsonValue::fromVariant(value));
}
