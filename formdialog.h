#ifndef FORMDIALOG_H
#define FORMDIALOG_H

#pragma once

#include <QObject>
#include <QDialog>
#include <QMessageBox>

#include <QDebug>

#include "configmanager.h"//配置文件系统
#include "logmanager.h"//日志系统

namespace Ui {
class FormDialog;
}


class FormDialog : public QDialog
{
    Q_OBJECT
public:
    explicit FormDialog(QWidget *parent = nullptr);
    ~FormDialog();
    void initData();
private:
    //参数不为空校验
    bool parameterIsNotEmptyCheck();
private slots:
    void btn_saveReportData();
private:
    Ui::FormDialog* ui;
};

#endif // FORMDIALOG_H
