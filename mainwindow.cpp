#include "mainwindow.h"
#include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow),startTheExperiment(new QTimer(this))
{
    LogManager::instance()->writeLog("程序开始启动", LogLevel::Info);
    ui->setupUi(this);
    this->setWindowTitle("机电联合破坏试验");
    //    this->setWindowFlags(Qt::FramelessWindowHint);
    init();//界面、数据初始化工作
    ui->btn_pause->hide();
}

MainWindow::~MainWindow()
{
    ConfigManager::instance()->set("userPermissions", "");
    ConfigManager::instance()->save();
    qDebug()<<"程序关闭";
    LogManager::instance()->writeLog("程序关闭", LogLevel::Info);
    delete ui;
}

void MainWindow::init()
{
    LogManager::instance()->writeLog("开始进行初始化操作", LogLevel::Info);
    initFrontEndPageDisplay();//显示前端界面
    initData();//数据初始化
    initButtonSlotSignals();//初始化信号槽函数
    LogManager::instance()->writeLog("初始化操作完成", LogLevel::Info);
}

void MainWindow::initFrontEndPageDisplay()
{
    upperPart();//上边功能展示
    leftDisplay();//左边功能展示
    rightDisplay();//右边功能展示
}

void MainWindow::initButtonSlotSignals()
{
    connect(ui->btn_startHighVoltage,&QPushButton::clicked,this,&MainWindow::btn_startHighVoltageSlots);//启动高压
    connect(ui->btn_closeHighVoltage,&QPushButton::clicked,this,&MainWindow::btn_closeHighVoltageSlots);//关闭高压
    connect(ui->btn_increasedPressure,&QPushButton::clicked,this,&MainWindow::btn_increasedPressureSlots);//加载
    connect(ui->btn_reduceStress,&QPushButton::clicked,this,&MainWindow::btn_reduceStressSlots);//卸载
    connect(ui->btn_reset,&QPushButton::clicked,this,&MainWindow::btn_resetSlots);//复位
    connect(ui->btn_pause,&QPushButton::clicked,this,&MainWindow::btn_pauseSlots);//暂停
    connect(ui->btn_oneClickReset,&QPushButton::clicked,this,&MainWindow::btn_oneClickResetSlots);//一键清零
    connect(ui->btn_startTheExperiment,&QPushButton::clicked,this,&MainWindow::btn_startTheExperimentSlots);//开始试验
    connect(ui->btn_stopTheExperiment,&QPushButton::clicked,this,&MainWindow::btn_stopTheExperimentSlots);//停止试验
    connect(ui->btn_reportReferences,&QPushButton::clicked,this,&MainWindow::btn_reportReferencesSlots);//报告录参
    connect(ui->btn_generateReport,&QPushButton::clicked,this,&MainWindow::btn_generateReportSlots);//生成报告
    connect(ui->btn_printingReports,&QPushButton::clicked,this,&MainWindow::btn_printingReportsSlots);//打印报告
    connect(ui->btn_userManagement,&QPushButton::clicked,this,&MainWindow::btn_userManagementSlots);//用户管理
    connect(ui->btn_logOut,&QPushButton::clicked,this,&MainWindow::btn_logOutSlots);//退出登录

    connect(startTheExperiment, &QTimer::timeout, this, &MainWindow::trialStart_upDataDisplay);//试验开始定时器启动后，读取数据
    connect(ModbusManager::instance(), &ModbusManager::voltageValueDataInsertion, this, &MainWindow::voltageValueConversionCurve);//曲线图中展示位移值
    connect(ModbusManager::instance(), &ModbusManager::tensionDataInserted, this, &MainWindow::tensionValueToCurveGraph);//曲线图中展示拉力值
    connect(ModbusManager::instance(),&ModbusManager::displacementDataInsertion,this,&MainWindow::displacementValueDataInsertion);//位移值

    connect(ModbusManager::instance(),&ModbusManager::connedtedFromPLC,this,[=](){
        ui->statusbar->showMessage("与PLC成功建立连接");
    });
    connect(ModbusManager::instance(),&ModbusManager::disconnectedFromPLC,this,[=](){
        ui->statusbar->showMessage("未与PLC建立连接");
    });

}

void MainWindow::initData()
{
    try {
        LogManager::instance()->writeLog("数据库管理系统开始初始化", LogLevel::Info);
        DatabaseManager::instance()->init();    //数据库管理系统初始化
        LogManager::instance()->writeLog("数据库管理系统完成初始化", LogLevel::Info);

        LogManager::instance()->writeLog("配置文件管理系统开始初始化", LogLevel::Info);
        ConfigManager::instance()->load();    //配置文件管理系统初始化
        LogManager::instance()->writeLog("配置文件管理系统完成初始化", LogLevel::Info);

        LogManager::instance()->writeLog("plc通讯管理模块开始初始化", LogLevel::Info);
        ModbusManager::instance()->init();    //plc通讯管理模块初始化
        LogManager::instance()->writeLog("plc通讯管理模块完成初始化", LogLevel::Info);

    } catch (const std::exception &e) {
        LogManager::instance()->writeLog("数据初始化失败!", LogLevel::Error);
        LogManager::instance()->writeLog(e.what(), LogLevel::Error);
    }catch (...) {
        LogManager::instance()->writeLog("数据初始化过程中发生未知异常", LogLevel::Error);
    }
}

void MainWindow::upperPart()
{
    initControlButton(ui->btn_startHighVoltage,"images/qdgy1.png","启动高压");
    initControlButton(ui->btn_closeHighVoltage,"images/gbgy2.png","关闭高压");
    initControlButton(ui->btn_increasedPressure,"images/jz.png","加载");
    initControlButton(ui->btn_reduceStress,"images/xz.png","卸载");
    initControlButton(ui->btn_reset,"images/fw.png","复位");
    initControlButton(ui->btn_pause,"images/zt.png","暂停");
    initControlButton(ui->btn_oneClickReset,"images/yjql.png","一键清零");
    initControlButton(ui->btn_startTheExperiment,"images/kssy.png","开始试验");
    initControlButton(ui->btn_stopTheExperiment,"images/tzsy.png","停止试验");
    initControlButton(ui->btn_reportReferences,"images/bglc.png","报告录参");
    initControlButton(ui->btn_generateReport,"images/scbg.png","生成报告");
    initControlButton(ui->btn_printingReports,"images/dybg.png","打印报告");
    initControlButton(ui->btn_userManagement,"images/yhgl.png","用户管理");
    initControlButton(ui->btn_logOut,"images/tcdl.png","退出登录");
}

void MainWindow::leftDisplay()
{
    ui->te_controlScheme->setReadOnly(true);//属性设置成只读
    ui->groupBox->setStyleSheet(R"(
                                QGroupBox {
                                font: bold 14pt 'Microsoft YaHei';
                                color: black;
                                }
                                QGroupBox::title {
                                subcontrol-origin: margin;
                                subcontrol-position: top left;
                                padding: 5px;
                                }
                                )");
}

void MainWindow::rightDisplay()
{
    voltageTimeGraphDisplay();//电压时间曲线图
    tensionTimeCurveDisplay();//拉力时间曲线图
    downTableDisplay();//下面表格部分展示
}

void MainWindow::voltageTimeGraphDisplay()
{
    seriesKVTime = new QLineSeries();
    seriesKVTime->setName("电压曲线");

    QChart *chart = new QChart();
    chart->addSeries(seriesKVTime);
    chart->setTitle("实时电压曲线(KV)");
    QFont titleFont;
    titleFont.setPointSize(12);     // 设置字体大小（例如 14pt）
    titleFont.setBold(true);        // 设置加粗
    chart->setTitleFont(titleFont); // 应用到标题
    chart->legend()->hide();
    chart->setBackgroundBrush(QBrush(QColor(255, 255, 255)));

    // 设置坐标轴
    QValueAxis *axisX = new QValueAxis();
    axisX->setRange(0, 300);
    axisX->setTitleText("时 间");
    axisX->setTickInterval(60);  // 每隔60单位显示一个刻度
    axisX->setTickCount(6);      // 设置刻度数，保证横轴上显示6个刻度（0, 60, 120, 180, 240, 300）
    axisX->setMinorTickCount(5); // 设置小刻度的数量，即每个大刻度之间有5个小刻度

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 120);
    //    axisY->setTitleText("位 移");
    axisY->setTickInterval(20);  // 每隔20单位显示一个刻度
    axisY->setTickCount(7);      // 设置刻度数，保证纵轴上显示7个刻度（0, 20, 40, 60, 80, 100, 120）
    axisY->setMinorTickCount(4); // 设置小刻度的数量，即每个大刻度之间有4个小刻度

    chart->addAxis(axisX, Qt::AlignBottom);
    seriesKVTime->attachAxis(axisX);

    chart->addAxis(axisY, Qt::AlignLeft);
    seriesKVTime->attachAxis(axisY);

    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);
    chartView->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 必须设置伸展策略

    // 添加到 widget_3 的栅格布局
    QGridLayout *layout = qobject_cast<QGridLayout*>(ui->DisplacementTimeWidget->layout());
    if (layout) {
        layout->addWidget(chartView, 0, 0); // 添加到第0行第0列
    }

}

void MainWindow::tensionTimeCurveDisplay()
{
    seriesKNTime = new QLineSeries();
    seriesKNTime->setName("载荷曲线");

    QChart *chart = new QChart();
    chart->addSeries(seriesKNTime);
    chart->setTitle("实时载荷曲线(KN)");
    QFont titleFont;
    titleFont.setPointSize(12);     // 设置字体大小（例如 14pt）
    titleFont.setBold(true);        // 设置加粗
    chart->setTitleFont(titleFont); // 应用到标题
    chart->legend()->hide();
    chart->setBackgroundBrush(QBrush(QColor(255, 255, 255)));

    // 设置坐标轴
    QValueAxis *axisX = new QValueAxis();
    axisX->setRange(0, 300);
    axisX->setTitleText("时 间");
    axisX->setTickInterval(60);  // 每隔60单位显示一个刻度
    axisX->setTickCount(6);      // 设置刻度数，保证横轴上显示6个刻度（0, 60, 120, 180, 240, 300）
    axisX->setMinorTickCount(5); // 设置小刻度的数量，即每个大刻度之间有5个小刻度

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 120);
    //    axisY->setTitleText("载\n荷");
    axisY->setTickInterval(20);  // 每隔20单位显示一个刻度
    axisY->setTickCount(7);      // 设置刻度数，保证纵轴上显示7个刻度（0, 20, 40, 60, 80, 100, 120）
    axisY->setMinorTickCount(4); // 设置小刻度的数量，即每个大刻度之间有4个小刻度

    chart->addAxis(axisX, Qt::AlignBottom);
    seriesKNTime->attachAxis(axisX);

    chart->addAxis(axisY, Qt::AlignLeft);
    seriesKNTime->attachAxis(axisY);

    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);
    chartView->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 必须设置伸展策略

    // 添加到 widget_3 的栅格布局
    QGridLayout *layout = qobject_cast<QGridLayout*>(ui->KNTimeWidget->layout());
    if (layout) {
        layout->addWidget(chartView, 0, 0); // 添加到第0行第0列
    }
}

void MainWindow::downTableDisplay()
{
    // 创建标签
    QLabel* label = new QLabel(" 序号", ui->tableWidget);
    label->setStyleSheet("color: black; background-color: transparent; font-weight: bold;");
    label->move(ui->tableWidget->verticalHeader()->width() / 2, ui->tableWidget->horizontalHeader()->height() / 2-6);
    label->raise();  // 确保标签显示在最上层

    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::ExtendedSelection);
    int rowTotal = ui->tableWidget->rowCount();
    int columnTotal = ui->tableWidget->columnCount();

    for (int row = 0; row < rowTotal; ++row) {
        for (int col = 0; col < columnTotal - 1; ++col) {
            QTableWidgetItem* item = ui->tableWidget->item(row, col);
            if (!item) {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(row, col, item);
            }
//            item->setFlags(item->flags() & ~Qt::ItemIsEditable);
            item->setTextAlignment(Qt::AlignCenter);  // ✅ 文本居中
        }
        // 最后一列设置 ComboBox
        QComboBox* comboBox = new QComboBox(ui->tableWidget);
        comboBox->addItems(QStringList() << "无" <<"已破坏" << "未破坏" << "已击穿" << "未击穿");
        ui->tableWidget->setCellWidget(row, columnTotal - 1, comboBox);
    }
    // 设置所有列宽度自动适应内容
    for (int col = 0; col < columnTotal; ++col) {
        ui->tableWidget->resizeColumnToContents(col);
    }

    //这个是点击选择某一行，然后将历史数据回溯到曲线图上
//    connect(ui->tableWidget,&QTableWidget::itemSelectionChanged, this, &MainWindow::onTableRowSelected);
}

QMap<QString, QString> MainWindow::loadBookmarkDataFromJson(const QString &jsonFilePath)
{
    QMap<QString, QString> bookmarkData;
    try {
        QFile file(jsonFilePath);

        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            LogManager::instance()->writeLog("无法打开 JSON 文件", LogLevel::Error);
            return bookmarkData;
        }

        QByteArray data = file.readAll();
        file.close();

        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            LogManager::instance()->writeLog("JSON 解析错误", LogLevel::Error);
            return bookmarkData;
        }

        if (!doc.isObject()) {
            LogManager::instance()->writeLog("JSON 根元素不是对象", LogLevel::Error);
            return bookmarkData;
        }

        LogManager::instance()->writeLog("开始加载配置数据", LogLevel::Info);
        QJsonObject obj = doc.object();
        for (auto it = obj.begin(); it != obj.end(); ++it) {
            bookmarkData.insert(it.key(), it.value().toString());
        }
        LogManager::instance()->writeLog("配置数据加载完成，共 " + QString::number(bookmarkData.size()) + " 条", LogLevel::Info);        return bookmarkData;
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("加载书签配置过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("加载书签配置过程中发生未知异常", LogLevel::Error);
    }
    return bookmarkData;
}

void MainWindow::fillWordBookmarks(const QString &templatePath, const QString &savePath, const QMap<QString, QString> &bookmarkValues)
{
    try {
        QAxObject* wordApp = new QAxObject("Word.Application");
        if(!wordApp || wordApp->isNull()){
            LogManager::instance()->writeLog("无法初始化 Word 应用程序对象", LogLevel::Error);
            delete wordApp;
            return;
        }
        wordApp->setProperty("Visible", false); // 可以设为 true 观察过程

        QAxObject* documents = wordApp->querySubObject("Documents");

        if (!documents || documents->isNull()) {
            LogManager::instance()->writeLog("无法获取 Documents 对象", LogLevel::Error);
            wordApp->dynamicCall("Quit()");
            delete wordApp;
            return;
        }

        QString absPath = QDir(qApp->applicationDirPath()).filePath(templatePath);
        QAxObject* document = documents->querySubObject("Open(const QString&)", QDir::toNativeSeparators(absPath));
        if (!document || document->isNull()) {
            LogManager::instance()->writeLog("无法打开模板文档：" + absPath, LogLevel::Error);
            wordApp->dynamicCall("Quit()");
            delete wordApp;
            return;
        }

        QAxObject* bookmarks = document->querySubObject("Bookmarks");
        if (!bookmarks || bookmarks->isNull()) {
            LogManager::instance()->writeLog("无法获取书签对象", LogLevel::Error);
        }
        for (auto it = bookmarkValues.constBegin(); it != bookmarkValues.constEnd(); ++it) {
            QString bookmarkName = it.key();
            QString value = it.value();

            bool exists = bookmarks->dynamicCall("Exists(const QString&)", bookmarkName).toBool();
            if (exists) {
                QAxObject* bookmark = document->querySubObject("Bookmarks(QVariant)", bookmarkName);
                QAxObject* range = bookmark->querySubObject("Range");
                if(range && !range->isNull()){
                    range->setProperty("Text",value);
                }else{
                    LogManager::instance()->writeLog("无法获取书签 Range：" + bookmarkName, LogLevel::Warning);
                }
            } else {
                LogManager::instance()->writeLog("书签"+bookmarkName+"不存在:", LogLevel::Error);
            }
        }

        document->dynamicCall("SaveAs(const QString&)", QDir::toNativeSeparators(savePath));
        document->dynamicCall("Close(bool)", false);
        wordApp->dynamicCall("Quit()");
        delete wordApp;
        LogManager::instance()->writeLog("Word 文档已生成并填充", LogLevel::Info);

    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("填充 Word 模板过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("填充 Word 模板过程中发生未知异常", LogLevel::Error);
    }
}

void MainWindow::initControlButton(GraphicTextButton *button, const QString &imagePath, const QString &text, const QSize &size)
{
    button->setGraphic(QPixmap(imagePath));
    button->setLabel(text);
    button->setMinimumSize(size);
    QFont font("Microsoft YaHei", 12, QFont::Bold);  // 字体更大更清晰
    button->setTextStyle(font, Qt::black);
    return;
    button->setIconRatio(0.75);  // 图像占比提高

}

void MainWindow::updateTableWidget(const QVariantMap &params)
{
    int rowToInsert = -1;
    int rowCount = ui->tableWidget->rowCount();

    // 遍历每一行的第0列（ID列）
    for (int i = 0; i < rowCount; ++i) {
        QTableWidgetItem* item = ui->tableWidget->item(i, 0);
        if (!item || item->text().isEmpty()) {
            rowToInsert = i;
            break;
        }
    }

    // 如果所有行都有数据，则插入新行
    if (rowToInsert == -1) {
        rowToInsert = rowCount;
        ui->tableWidget->insertRow(rowToInsert);
    }

    QString sql = "select * from TestRecord where id = :id";
    QVariantMap param;
    param["id"] = params.value("id");

    QSqlQuery query = DatabaseManager::instance()->executeQuery(sql, param);

    while(query.next()){
        ui->tableWidget->setItem(rowToInsert, 0, new QTableWidgetItem(query.value("id").toString()));
        ui->tableWidget->setItem(rowToInsert, 1, new QTableWidgetItem(query.value("reportId").toString()));
        ui->tableWidget->setItem(rowToInsert, 2, new QTableWidgetItem(query.value("testMethod").toString()));
        ui->tableWidget->setItem(rowToInsert, 3, new QTableWidgetItem(query.value("sfl").toString()));
        ui->tableWidget->setItem(rowToInsert, 4, new QTableWidgetItem(query.value("holdTime").toString()));
        ui->tableWidget->setItem(rowToInsert, 5, new QTableWidgetItem(query.value("force").toString()));
        ui->tableWidget->setItem(rowToInsert, 6, new QTableWidgetItem(query.value("voltage").toString()));
        ui->tableWidget->setItem(rowToInsert, 7, new QTableWidgetItem(query.value("displacement").toString()));
        ui->tableWidget->setItem(rowToInsert, 8, new QTableWidgetItem(query.value("timestamp").toString()));
        ui->tableWidget->setItem(rowToInsert, 9, new QTableWidgetItem(query.value("operator").toString()));
        int comboColumn = ui->tableWidget->columnCount() - 1;
        QComboBox* combo = qobject_cast<QComboBox*>(ui->tableWidget->cellWidget(rowToInsert, comboColumn));
        if (combo) {
            int index = combo->findText(query.value("result").toString());
            if (index != -1) {
                combo->setCurrentIndex(index);
            }
        }
    }

    int rowTotal = ui->tableWidget->rowCount();
    int columnTotal = ui->tableWidget->columnCount();
    // 设置所有列宽度自动适应内容
    for (int col = 0; col < columnTotal; ++col) {
        ui->tableWidget->resizeColumnToContents(col);
    }

    for (int row = 0; row < rowTotal; ++row) {
        for (int col = 0; col < columnTotal - 1; ++col) {
            QTableWidgetItem* item = ui->tableWidget->item(row, col);
            item->setTextAlignment(Qt::AlignCenter);  // ✅ 文本居中
        }
    }
}

void MainWindow::insertTestData()
{
    QList<int> selectedRows;
    QItemSelectionModel *selectionModel = ui->tableWidget->selectionModel();

    // 获取选中的行号（去重）
    for (const QModelIndex &index : selectionModel->selectedIndexes()) {
        if (!selectedRows.contains(index.row())) {
            selectedRows.append(index.row());
        }
    }

    // 按照选择顺序处理每一行
    for (int i = 0; i < selectedRows.size(); ++i) {
        int row = selectedRows[i];
        QString loadKey = QString("负荷%1").arg(i + 1);
        QString voltageKey = QString("电压%1").arg(i + 1);
        QString resultKey = QString("结果%1").arg(i + 1);

        QString loadValue = ui->tableWidget->item(row, 5)->text();
        QString voltageValue = ui->tableWidget->item(row, 6)->text();
        QString resultValue;
        QWidget *widget = ui->tableWidget->cellWidget(row, 10);         // 第2列是嵌入的ComboBox
        if (QComboBox *combo = qobject_cast<QComboBox *>(widget)) {
            resultValue = combo->currentText();
        } else {
            resultValue = "";
        }

        ConfigManager::instance()->set(loadKey, loadValue);
        ConfigManager::instance()->set(voltageKey, voltageValue);
        ConfigManager::instance()->set(resultKey, resultValue);
    }

    // 清空未使用的项（最多6组）
    for (int i = selectedRows.size(); i < 6; ++i) {
        ConfigManager::instance()->set(QString("负荷%1").arg(i + 1), "");
        ConfigManager::instance()->set(QString("电压%1").arg(i + 1), "");
        ConfigManager::instance()->set(QString("结果%1").arg(i + 1), "");
    }

    ConfigManager::instance()->set("试验结论", ui->cb_testConclusion->currentText());
    ConfigManager::instance()->save();
}

//先读取plc中高压的值是多少，当值是0时，说明未启动，当值是非零时，说明已启动，或者通过高压启动状态判断
void MainWindow::btn_startHighVoltageSlots()
{
    return;
    try {
        LogManager::instance()->writeLog("点击启动高压按钮", LogLevel::Info);
        if (ModbusManager::instance()->isConnected()) {
            int address = ConfigManager::instance()->get("plc_startHighVoltage_registerAddress").toInt();
            int val = ConfigManager::instance()->get("plc_startHighVoltage_registerValue").toInt();
            ModbusManager::instance()->writeStartHighVoltageSignal(address, val);
            QMessageBox::information(this, "指令发送", "启动高压指令已执行");
            LogManager::instance()->writeLog("启动高压指令已执行", LogLevel::Info);
        } else {
            QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
            LogManager::instance()->writeLog("启动高压命令发送失败", LogLevel::Warning);
        }
    } catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("发送启动高压命令时捕获异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("发送启动高压命令时发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
    }
}

void MainWindow::btn_closeHighVoltageSlots()
{
    return;
    try {
        LogManager::instance()->writeLog("点击关闭高压按钮", LogLevel::Info);
        if (ModbusManager::instance()->isConnected()) {
            int address = ConfigManager::instance()->get("plc_closeHighVoltage_registerAddress").toInt();
            int val = ConfigManager::instance()->get("plc_closeHighVoltage_registerValue").toInt();
            ModbusManager::instance()->writeCloseHighVoltageSignal(address, val);
            QMessageBox::information(this, "指令发送", "关闭高压指令已执行");
            LogManager::instance()->writeLog("关闭高压指令已执行", LogLevel::Info);
        } else {
            QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
            LogManager::instance()->writeLog("关闭高压命令发送失败", LogLevel::Warning);
        }
    } catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("发送关闭高压命令时捕获异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("发送关闭高压命令时发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
    }
}

void MainWindow::btn_increasedPressureSlots()
{
    return;
    try {
        LogManager::instance()->writeLog("点击加载按钮", LogLevel::Info);
        if (ModbusManager::instance()->isConnected()) {
            int address = ConfigManager::instance()->get("plc_increasedPressure_registerAddress").toInt();
            int val = ConfigManager::instance()->get("plc_increasedPressure_registerValue").toInt();
            ModbusManager::instance()->writeLoadSignal(address, val);
            QMessageBox::information(this, "指令发送", "加载指令已执行");
            LogManager::instance()->writeLog("加载指令已执行", LogLevel::Info);
        } else {
            QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
            LogManager::instance()->writeLog("加载命令发送失败", LogLevel::Warning);
        }
    } catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("发送加载命令时捕获异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("发送加载命令时发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
    }
}

void MainWindow::btn_reduceStressSlots()
{
    return;
    try {
        LogManager::instance()->writeLog("点击卸载按钮", LogLevel::Info);
        if (ModbusManager::instance()->isConnected()) {
            int address = ConfigManager::instance()->get("plc_reduceStress_registerAddress").toInt();
            int val = ConfigManager::instance()->get("plc_reduceStress_registerValue").toInt();
            ModbusManager::instance()->writeUnistallSignal(address, val);
            QMessageBox::information(this, "指令发送", "卸载指令已执行");
            LogManager::instance()->writeLog("卸载指令已执行", LogLevel::Info);
        } else {
            QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
            LogManager::instance()->writeLog("卸载命令发送失败", LogLevel::Warning);
        }
    } catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("发送卸载命令时捕获异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("发送卸载命令时发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
    }
}

void MainWindow::btn_resetSlots()
{
    return;
    try {
        LogManager::instance()->writeLog("点击复位按钮", LogLevel::Info);
        if (ModbusManager::instance()->isConnected()) {
            int address = ConfigManager::instance()->get("plc_reset_registerAddress").toInt();
            int val = ConfigManager::instance()->get("plc_reset_registerValue").toInt();
            ModbusManager::instance()->writeResetSignal(address, val);
            QMessageBox::information(this, "指令发送", "复位指令已执行");
            LogManager::instance()->writeLog("复位指令已执行", LogLevel::Info);
        } else {
            QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
            LogManager::instance()->writeLog("复位命令发送失败", LogLevel::Warning);
        }
    } catch (const std::exception& e) {
        QString errMsg = QString::fromStdString(e.what());
        LogManager::instance()->writeLog("发送复位命令时捕获异常：" + errMsg, LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
    } catch (...) {
        LogManager::instance()->writeLog("发送复位命令时发生未知异常", LogLevel::Error);
        QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
    }
}

void MainWindow::btn_pauseSlots()
{
    return;
    QString label = ui->btn_pause->getLabel();
    if(label == "暂停"){
        try {
            LogManager::instance()->writeLog("点击暂停按钮", LogLevel::Info);
            if (ModbusManager::instance()->isConnected()) {
                int address = ConfigManager::instance()->get("plc_pause_registerAddress").toInt();
                int val = ConfigManager::instance()->get("plc_pause_registerValue").toInt();
                ModbusManager::instance()->writeStopSignal(address, val);
                QMessageBox::information(this, "指令发送", "暂停指令已执行");
                LogManager::instance()->writeLog("暂停指令已执行", LogLevel::Info);
                ui->btn_pause->setGraphic(QPixmap("images/jx.png"));
                ui->btn_pause->setLabel("继续");
            } else {
                QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
                LogManager::instance()->writeLog("暂停命令发送失败", LogLevel::Warning);
            }
        } catch (const std::exception& e) {
            QString errMsg = QString::fromStdString(e.what());
            LogManager::instance()->writeLog("发送暂停命令时捕获异常：" + errMsg, LogLevel::Error);
            QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
        } catch (...) {
            LogManager::instance()->writeLog("发送暂停命令时发生未知异常", LogLevel::Error);
            QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
        }
    }else{
        try {
            LogManager::instance()->writeLog("点击继续按钮", LogLevel::Info);
            if (ModbusManager::instance()->isConnected()) {
                int address = ConfigManager::instance()->get("plc_continue_registerAddress").toInt();
                int val = ConfigManager::instance()->get("plc_continue_registerValue").toInt();
                ModbusManager::instance()->writeContinueSignal(address, val);
                QMessageBox::information(this, "指令发送", "继续指令已执行");
                LogManager::instance()->writeLog("继续指令已执行", LogLevel::Info);
                ui->btn_pause->setGraphic(QPixmap("images/zt.png"));
                ui->btn_pause->setLabel("暂停");
            } else {
                QMessageBox::warning(this, "通信异常", "未连接到设备，请检查网络或PLC状态");
                LogManager::instance()->writeLog("继续命令发送失败", LogLevel::Warning);
            }
        } catch (const std::exception& e) {
            QString errMsg = QString::fromStdString(e.what());
            LogManager::instance()->writeLog("发送继续命令时捕获异常：" + errMsg, LogLevel::Error);
            QMessageBox::critical(this, "异常", "系统发生异常：" + errMsg);
        } catch (...) {
            LogManager::instance()->writeLog("发送继续命令时发生未知异常", LogLevel::Error);
            QMessageBox::critical(this, "异常", "系统发生未知异常，请查看日志");
        }
    }
}

void MainWindow::btn_oneClickResetSlots()
{
    LogManager::instance()->writeLog("点击一键清零按钮", LogLevel::Info);

    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(this, "一键清零", "是否执行一键清零？",QMessageBox::Yes | QMessageBox::No);

    if(reply == QMessageBox::Yes) {
        LogManager::instance()->writeLog("用户确认一键清零操作", LogLevel::Info);
        ui->te_controlScheme->setText("");
        ui->la_pullVal->setText("0.00");
        ui->la_currentVoltageVal->setText("0.00");
        ui->la_displacementVal->setText("0.00");
        seriesKNTime->clear();
        seriesKVTime->clear();
    } else {
        LogManager::instance()->writeLog("用户取消一键清零操作", LogLevel::Info);
        return;
    }
}

/*
开始试验时需要检查电压和填写的是否一致  还要判断连接是否已连接  这里需要加上一些trycatch语句   还要加上试验方式校验 还有一些其他参数
*/
void MainWindow::btn_startTheExperimentSlots()
{
    LogManager::instance()->writeLog("点击开始试验按钮", LogLevel::Info);

    QString selectedText = ui->cb_testMethod->currentText();
    if (selectedText == "无") {
        QMessageBox::warning(this, "提示", "请选择有效的试验方式！");
        return;
    }

    if(ui->le_highPressureValue->text().trimmed().isEmpty()){
        QMessageBox::warning(this, "提示", "高压值不能为空");
        return;
    }

    double actualVoltage = ui->la_currentVoltageVal->text().toDouble();
    double inputVoltage = ui->le_highPressureValue->text().toDouble();
    double tolerance = 0.5;
    if (qAbs(actualVoltage - inputVoltage) > tolerance) {
        QMessageBox::warning(this, "提示", "电压值不一致");
        return;
    }

    if (ui->le_SFLValue->text().trimmed().isEmpty()) {
        QMessageBox::warning(this, "提示", "SFL值不能为空");
        return;
    }


    if(ui->le_keepTime->text().trimmed().isEmpty()){
        QMessageBox::warning(this, "提示", "保持时间不能为空");
        return;
    }

    if(ui->le_numberOfTrials->text().trimmed().isEmpty()){
        QMessageBox::warning(this, "提示", "试验次数不能为空");
        return;
    }

    //与plc连接是否异常
    bool flag = ModbusManager::instance()->isConnected();
    if(flag){
        QMessageBox::information(this,"提示","与plc连接异常，请查看");
        return;
    }

    testCount++;//试验次数+1
    if(testCount>6){
        QMessageBox::information(this,"提示","试验次数已达到最大值，请重新启动程序，启动程序前请打印输出报告");
        return;
    }

    //开始试验之前，先将数据清空
    ui->te_controlScheme->setText("");
    ui->la_pullVal->setText("0.00");
    ui->la_currentVoltageVal->setText("0.00");
    ui->la_displacementVal->setText("0.00");
    seriesKNTime->clear();
    seriesKVTime->clear();

    //先往TestReport表中插入一条数据，确定好ID，然后，拉力值和电压值使用表中生成的ID,用于后续数据回溯
    //试验完成后基于试验数据修改 指定ID TestReport表。
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMddhhmm");
    ConfigManager::instance()->set("testId", timestamp);
    ConfigManager::instance()->save();

    startTime = QDateTime::currentDateTime();
    startTheExperiment->start(50);
}

void MainWindow::btn_stopTheExperimentSlots()
{
    LogManager::instance()->writeLog("点击停止试验按钮", LogLevel::Info);
    startTheExperiment->stop();
    QThread::msleep(300);//等待300毫秒，
    QMessageBox::information(this, "提示", "点击停止试验按钮");
    //插入一条数据，并将结果显示在table中
    QString sql = "INSERT INTO TestRecord (id,reportId, timestamp, operator, testMethod,sfl, holdTime, force, voltage, displacement, result) "
                  "VALUES (:id, :reportId, :timestamp, :operator, :testMethod,:sfl, :holdTime,"
                  "(select max(tension_value) from tension_measurement where trace_id = :id), "
                  "(select max(voltage_value) from voltage_measurement where trace_id = :id),"
                  "(select max(displacement_value) from displacement_measurement where trace_id = :id),:result)";
    QVariantMap params;
    params["id"] = ConfigManager::instance()->get("testId").toString();
    params["reportId"] = ConfigManager::instance()->get("reportNo").toString();
    params["timestamp"] = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    params["operator"] = ConfigManager::instance()->get("userPermissions").toString();
    params["testMethod"] = ui->cb_testMethod->currentText();
    params["sfl"] = ui->le_SFLValue->text();
    params["holdTime"] = ui->le_keepTime->text();
    try {
        bool success = DatabaseManager::instance()->execTransactional(sql, params);
        //插入成功之后，同时将数据同步到tableWidget上
        if(success){
            LogManager::instance()->writeLog("TestRecord数据插入成功", LogLevel::Info);
            //将值插入到tableWidget中，
//            updateTableWidget(params);
            qDebug()<<"成功";
        }else{
            LogManager::instance()->writeLog("TestRecord数据插入失败！", LogLevel::Info);
            qDebug()<<"失败";
        }
        updateTableWidget(params);

    }catch (const std::exception& ex) {
        LogManager::instance()->writeLog(QString("数据库操作异常：%1").arg(ex.what()), LogLevel::Error);
        QMessageBox::critical(this, "异常", "数据库操作发生异常，请检查日志！");
    }
}

void MainWindow::btn_reportReferencesSlots()
{
    LogManager::instance()->writeLog("点击报告录参按钮", LogLevel::Info);
    FormDialog dlg(this);
    if(dlg.exec() ==QDialog::Accepted){
        QMessageBox::information(this, "提示", "保存成功！");
    }
}

//这里暂时先这样做，后续可以用子线程去生成报告
//这里还有字体样式未完成修改 还有表格中的参数也没有插入到word文档中
//再加一步操作，文档默认转成pdf 放置到指定文件夹中，以防后续漏打印
void MainWindow::btn_generateReportSlots()
{
    LogManager::instance()->writeLog("点击生成报告按钮", LogLevel::Info);

    QString selectedText = ui->cb_testConclusion->currentText();
    if (selectedText == "无") {
        QMessageBox::warning(this, "提示", "请选择一个有效的选项！");
        return;
    }

    QSet<int> uniqueRows;
    QList<QTableWidgetSelectionRange> ranges = ui->tableWidget->selectedRanges();
    for (const QTableWidgetSelectionRange& range : ranges) {
        for (int row = range.topRow(); row <= range.bottomRow(); ++row) {
            uniqueRows.insert(row);
        }
    }

    if (uniqueRows.size() != ui->le_numberOfTrials->text().toInt()) {
        QMessageBox::information(this, "提示", "试验次数和数据数不匹配！");
        return;
    }

    int lastColumn = ui->tableWidget->columnCount() - 1;
    for(int row :uniqueRows){
        QWidget* widget = ui->tableWidget->cellWidget(row, lastColumn);
        if (QComboBox* comboBox = qobject_cast<QComboBox*>(widget)) {
            QString value = comboBox->currentText();
            if (value == "无") {
                QMessageBox::warning(this, "提示", QString("第 %1 行的结论为“无”，请修改后再生成报告。").arg(row + 1));
                return;
            }
        } else {
            QMessageBox::warning(this, "提示", QString("第 %1 行的最后一列不是有效的下拉框控件。").arg(row + 1));
            return;
        }
    }

    //试验数据插入到配置标签中，然后等待插件自动生成
    insertTestData();

    //将现有的数据插入到json文件中，然后下面再生成
    QString filter = "Word 文档 (*.doc)";
    QString filePath = QFileDialog::getSaveFileName(this, "保存报告文档", "复合绝缘子报告", filter);
    if (filePath.isEmpty()) {
        LogManager::instance()->writeLog("用户取消保存", LogLevel::Info);
        return;
    }

    QString jsonPath = "config/action.json";
    QMap<QString, QString> bookmarkValues = loadBookmarkDataFromJson(jsonPath);

    LogManager::instance()->writeLog("准备启动子线程生成报告", LogLevel::Info);

    // 创建线程对象
    QThread* thread = new QThread;

    // 使用 lambda 封装任务
    QObject* worker = new QObject; // 占位对象，用于绑定槽
    worker->moveToThread(thread);

    connect(thread, &QThread::started, worker, [=]() {
        try {
            LogManager::instance()->writeLog("子线程开始生成报告", LogLevel::Info);
            fillWordBookmarks("config/reportDemo.doc", filePath, bookmarkValues);
            LogManager::instance()->writeLog("报告生成完毕", LogLevel::Info);
            QMetaObject::invokeMethod(this, [=]() {QMessageBox::information(this, "提示", "报告生成完毕");}, Qt::QueuedConnection);
        } catch (const std::exception& e) {
            QString errMsg = QString::fromUtf8(e.what());
            LogManager::instance()->writeLog("报告生成失败!", LogLevel::Error);
            LogManager::instance()->writeLog(errMsg, LogLevel::Error);

            QMetaObject::invokeMethod(this, [=]() {
                QMessageBox::critical(this, "错误", "报告生成失败: " + errMsg);
            }, Qt::QueuedConnection);
        }
        thread->quit(); // 任务完成后退出线程
    });

    // 清理资源
    connect(thread, &QThread::finished, worker, &QObject::deleteLater);
    connect(thread, &QThread::finished, thread, &QObject::deleteLater);

    thread->start();
}


void MainWindow::btn_printingReportsSlots()
{
    try {
        LogManager::instance()->writeLog("点击打印报告按钮", LogLevel::Info);

        QString filter = "Word 文档 (*.doc *.docx)";
        QString filePath = QFileDialog::getOpenFileName(this, "选择 Word 文件", QDir::homePath(), filter);

        if (filePath.isEmpty()) {
            LogManager::instance()->writeLog("用户未选择任何文件", LogLevel::Info);
        } else {
            LogManager::instance()->writeLog("选择的文件路径为:"+filePath, LogLevel::Info);
            QMessageBox::information(this, "提示", "开始进行打印报告");
            QAxObject wordApp("Word.Application");
            wordApp.setProperty("Visible", false);

            QAxObject *documents = wordApp.querySubObject("Documents");
            QAxObject *document = documents->querySubObject("Open(const QString&)", filePath);
            document->dynamicCall("PrintOut()"); // 执行打印
            document->dynamicCall("Close()");
            wordApp.dynamicCall("Quit()");
            LogManager::instance()->writeLog("报告打印完成", LogLevel::Info);
            QMessageBox::information(this, "提示", "报告打印完成");
        }
    } catch (const std::exception &e) {
        LogManager::instance()->writeLog("报告打印失败!", LogLevel::Error);
        LogManager::instance()->writeLog(e.what(), LogLevel::Error);
        QMessageBox::information(this, "Error", "报告打印错误,原因:"+QString::fromUtf8(e.what()));
    }
}

/*
直接给一个高权限的用户和多个低权限的用户
点击这个按钮的时候，需要看下是否是高权限用户，不是则提示权限不足，若是则弹出用户管理界面，高权限用户可以在里面添加低权限用户
*/
void MainWindow::btn_userManagementSlots()
{
    LogManager::instance()->writeLog("点击用户管理按钮", LogLevel::Info);
    QString role = ConfigManager::instance()->get("userPermissions").toString();
    if(role!="admin"){
        QMessageBox::information(this,"提示","你的权限不足，无法操作！");
        return;
    }

    UserManager userManager(this);
    if(userManager.exec() ==QDialog::Accepted){
        QMessageBox::information(this, "提示", "保存成功！");
    }

}

//主要是为了和登录形成对应关系，这个仅输出提示语句就好
//登录时登录人的信息填写在配置文件中或者是其他地方，退出登录时需要将其清除或更正
void MainWindow::btn_logOutSlots()
{
    int ret = QMessageBox::question(this, "退出登录确认", "确定要退出当前登录吗？", QMessageBox::Yes | QMessageBox::No);
    if (ret != QMessageBox::Yes) {
        LogManager::instance()->writeLog("用户取消退出登录", LogLevel::Info);
        return;
    }

    LogManager::instance()->writeLog("点击退出登录按钮", LogLevel::Info);
    ConfigManager::instance()->set("userPermissions", "");
    ConfigManager::instance()->save();
    this->close();//隐藏主界面
    QApplication::quit();
}

void MainWindow::trialStart_upDataDisplay()
{
    ModbusManager::instance()->readVoltageValueAndReturnSignal();//读电压值并返回信号
    ModbusManager::instance()->readTheTensionValueAndReturnTheSignal();//读拉力值并返回信号
    ModbusManager::instance()->readTheDisplacementValueAndReturnTheSignal();//读位移值并返回信号
}

void MainWindow::tensionValueToCurveGraph(const QDateTime& timestamp, double tension)
{
    try {
        static double maxTension = 0.0;  // 用于记录当前最大拉力值
        double x = startTime.msecsTo(timestamp) / 1000.0; // 将时间映射为横轴秒数
        if(seriesKNTime){
            seriesKNTime->append(QPointF(x, tension));
        }
        // 更新最大值逻辑
        if (tension > maxTension) {
            maxTension = tension;
            ui->la_pullVal->setText(QString::number(maxTension,'f',2));
        }
        // 检查是否下降超过阈值
        // 这里是检测到有下降的信号后，发送指令停止试验，同时确定下是否我这边要关闭，要手动关闭还是自动关闭试验
        if (tension < maxTension - 5.0) {
            QString msg = QString("警告：拉力值下降超过阈值！当前值：%1，最大值：%2")
                    .arg(tension, 0, 'f', 2).arg(maxTension, 0, 'f', 2);
            LogManager::instance()->writeLog(msg, LogLevel::Warning);
            qDebug() << msg; // 可选：调试输出
        }
        if(x>=300){
            startTheExperiment->stop();
        }
    }  catch (const std::exception& e) {
        LogManager::instance()->writeLog("绘制拉力图过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("绘制拉力图过程中发生未知异常", LogLevel::Error);
    }

}

void MainWindow::displacementValueDataInsertion(double tension)
{
    QString requestVal = QString::number(tension,'f',2);
    QString val = ui->la_displacementVal->text();
    if(val!=requestVal){
        ui->la_displacementVal->setText(requestVal);
    }
}

void MainWindow::onTableRowSelected()
{
    QList<QTableWidgetSelectionRange> ranges = ui->tableWidget->selectedRanges();
    if (ranges.isEmpty()) return;

    // 只取第一个选区的第一行
    int selectedRow = ranges.first().topRow();
    QTableWidgetItem* item = ui->tableWidget->item(selectedRow, 0); // 第一列
    if (item) {
        QString value = item->text();
        qDebug() << "选中行的第一列值:" << value;
        //下面是拉力曲线图数据
        QString sql = "select timestamp,tension_value from tension_measurement where trace_id = :id order by timestamp asc";
        QVariantMap param;
        param["id"] = value;
        QSqlQuery query = DatabaseManager::instance()->executeQuery(sql, param);
        // 清空旧图像数据
        if (seriesKNTime) {
            seriesKNTime->clear();
        }
        // 重置最大拉力值
        bool firstRow = true;
        while (query.next()) {
            QString timestampStr = query.value("timestamp").toString();
            double tension = query.value("tension_value").toDouble();
            QDateTime timestamp = QDateTime::fromString(timestampStr, Qt::ISODate);
            if (!timestamp.isValid()) {
                qDebug() << "无效时间格式:" << timestampStr;
                continue;
            }
            // 设置起始时间为第一条数据时间
            if (firstRow) {
                startTime = timestamp;
                firstRow = false;
            }
            // 插入点到图中
            double x = startTime.msecsTo(timestamp) / 1000.0; // 将时间映射为横轴秒数
            if(seriesKNTime){
                seriesKNTime->append(QPointF(x, tension));
            }
        }
        QString sql1 = "select max(tension_value) as max_value from tension_measurement where trace_id = :id";
        QVariantMap param1;
        param1["id"] = value;
        QSqlQuery query1 = DatabaseManager::instance()->executeQuery(sql1, param1);
        while (query1.next()) {
            QString timestampStr = query1.value("max_value").toString();
            qDebug()<<timestampStr<<"ddsdsdsdsds";
            ui->la_pullVal->setText(timestampStr);
        }

        //下面是电压曲线图数据
        QString sql2 = "select timestamp,voltage_value from voltage_measurement where trace_id = :id order by timestamp asc";
        QVariantMap param2;
        param2["id"] = value;
        QSqlQuery query2 = DatabaseManager::instance()->executeQuery(sql2, param2);
        // 清空旧图像数据
        if (seriesKVTime) {
            seriesKVTime->clear();
        }
        // 重置最大拉力值
        bool firstRow1 = true;
        while (query2.next()) {
            QString timestampStr = query2.value("timestamp").toString();
            double tension = query2.value("voltage_value").toDouble();
            QDateTime timestamp = QDateTime::fromString(timestampStr, Qt::ISODate);
            if (!timestamp.isValid()) {
                qDebug() << "无效时间格式:" << timestampStr;
                continue;
            }
            // 设置起始时间为第一条数据时间
            if (firstRow1) {
                startTime = timestamp;
                firstRow1 = false;
            }
            // 插入点到图中
            double x = startTime.msecsTo(timestamp) / 1000.0; // 将时间映射为横轴秒数
            if(seriesKVTime){
                seriesKVTime->append(QPointF(x, tension));
            }
        }
        QString sql3 = "select max(voltage_value) as max_value from voltage_measurement where trace_id = :id";
        QVariantMap param3;
        param3["id"] = value;
        QSqlQuery query3 = DatabaseManager::instance()->executeQuery(sql3, param3);
        while (query3.next()) {
            QString timestampStr = query3.value("max_value").toString();
            qDebug()<<timestampStr<<"ddsdsdsdsds";
            ui->la_currentVoltageVal->setText(timestampStr);
        }

        LogManager::instance()->writeLog("拉力数据加载完成，trace_id=" + value, LogLevel::Info);
    } else {
        qDebug() << "选中行的第一列为空";
    }
}

void MainWindow::voltageValueConversionCurve(const QDateTime& timestamp, double displacement)
{
    try {
        static double maxDisplacement = 0.0;  // 用于记录当前最大拉力值
        double x = startTime.msecsTo(timestamp) / 1000.0; // 将时间映射为横轴秒数
        if(seriesKVTime){
            seriesKVTime->append(QPointF(x, displacement));
        }

        // 更新最大值逻辑
        if (displacement > maxDisplacement) {
            maxDisplacement = displacement;
            ui->la_currentVoltageVal->setText(QString::number(maxDisplacement,'f',2));
        }
        // 检查是否下降超过阈值
        // 这里是检测到有下降的信号后，发送指令停止试验，同时确定下是否我这边要关闭，要手动关闭还是自动关闭试验
        if (displacement < maxDisplacement - 5.0) {
            QString msg = QString("警告：拉力值下降超过阈值！当前值：%1，最大值：%2")
                    .arg(displacement, 0, 'f', 2).arg(maxDisplacement, 0, 'f', 2);
            LogManager::instance()->writeLog(msg, LogLevel::Warning);
            qDebug() << msg; // 可选：调试输出
        }

        if(x>=300){
            startTheExperiment->stop();
        }
    }
    catch (const std::exception& e) {
        LogManager::instance()->writeLog("绘制拉力图过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("绘制拉力图过程中发生未知异常", LogLevel::Error);
    }
}
