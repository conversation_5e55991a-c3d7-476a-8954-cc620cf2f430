#include "modbusmanager.h"

ModbusManager *ModbusManager::instance()
{
    static ModbusManager* singleton = new ModbusManager();
    return singleton;
}

ModbusManager::ModbusManager(QObject *parent):QObject(parent)
  ,modbusClient(new QModbusTcpClient(this)),connectTimer(new QTimer(this)),conditionMonitoring(new QTimer(this)),plcConnectFlag(false)
{   
}

void ModbusManager::init()
{
    try {
        //构造函数内部可初始化资源
        plc_ip = ConfigManager::instance()->get("plc_ip").toString();
        plc_port = ConfigManager::instance()->get("plc_port").toInt();

        qDebug()<<plc_ip<<" ++  "<<plc_port<<ConfigManager::instance()->get("plc_ip").toString();
        if(!modbusClient){
            LogManager::instance()->writeLog("modbusClient 未初始化", LogLevel::Error);
            return;
        }
        // 设置连接参数
        modbusClient->setConnectionParameter(QModbusDevice::NetworkAddressParameter, plc_ip);
        modbusClient->setConnectionParameter(QModbusDevice::NetworkPortParameter, plc_port);
        modbusClient->setTimeout(1000);         // 通信超时，毫秒
        modbusClient->setNumberOfRetries(3);    // 通信重试次数

        connect(modbusClient,&QModbusTcpClient::stateChanged,this,&ModbusManager::plcConnectState);
        connect(modbusClient,&QModbusClient::errorOccurred, this,&ModbusManager::plcConnectError);

        connect(connectTimer,&QTimer::timeout,this,[=](){
            if(!plcConnectFlag){            modbusClient->connectDevice();        }
        });
        connect(conditionMonitoring, &QTimer::timeout, this, &ModbusManager::checkHeartbeat);

        modbusClient->connectDevice();
        LogManager::instance()->writeLog("Modbus 初始化完成，开始连接 PLC：" + plc_ip + ":" + QString::number(plc_port), LogLevel::Info);
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("Modbus 初始化发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("Modbus 初始化发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeRegisterValue(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}


bool ModbusManager::isConnected() const
{
    return (modbusClient && modbusClient->state() == QModbusDevice::ConnectedState);
}

void ModbusManager::readVoltageValueAndReturnSignal()
{
    qDebug()<<ConfigManager::instance()->get("testId").toString();
    try {
        //        int statusAddress = ConfigManager::instance()->get("plc_displacementTime").toInt();
        //两个16位的寄存器拼接成一个32位的二进制数，将二进制数据解释为 IEEE 754 格式的浮点数
        QModbusDataUnit unit(QModbusDataUnit::HoldingRegisters, 12, 2);//10进制 高低位 转32位浮点

        if (auto* reply = modbusClient->sendReadRequest(unit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    quint16 status = reply->result().value(0);
                    quint16 status1 = reply->result().value(1);
                    quint32 combined = (static_cast<quint32>(status1) << 16) | status;
                    float resultFloat;
                    memcpy(&resultFloat, &combined, sizeof(resultFloat));
                    resultFloat = round(resultFloat * 100.0f) / 100.0f;

                    QVariantMap params;
                    params["trace_id"] = ConfigManager::instance()->get("testId").toString(); // 测试标识符
                    params["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate); // 建议用 ISO 格式
                    params["voltage_value"] = resultFloat; // 实际电压值，单位 KV
                    QString sql = R"(
                                  INSERT INTO voltage_measurement (trace_id, timestamp, voltage_value)
                                  VALUES (:trace_id, :timestamp, :voltage_value)
                                  )";
                    bool ok = DatabaseManager::instance()->execTransactional(sql, params);
                    if (!ok) {
                        LogManager::instance()->writeLog("写入高压数据失败", LogLevel::Error);
                    }
                    emit voltageValueDataInsertion(QDateTime::currentDateTime(),resultFloat);
                } else {
                    LogManager::instance()->writeLog("运行状态读取失败：" + reply->errorString(), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送运行状态读取请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("高压读取流程异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("高压读取流程发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::readTheTensionValueAndReturnTheSignal()
{
    try {
        //        int statusAddress = ConfigManager::instance()->get("plc_displacementTime").toInt();
        //两个16位的寄存器拼接成一个32位的二进制数，将二进制数据解释为 IEEE 754 格式的浮点数
        QModbusDataUnit unit(QModbusDataUnit::HoldingRegisters, 122, 2);//10进制 高低位 转32位浮点

        if (auto* reply = modbusClient->sendReadRequest(unit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    quint16 status = reply->result().value(0);
                    quint16 status1 = reply->result().value(1);
                    quint32 combined = (static_cast<quint32>(status1) << 16) | status;
                    float resultFloat;
                    memcpy(&resultFloat, &combined, sizeof(resultFloat));
                    resultFloat = round(resultFloat * 100.0f) / 100.0f;

                    QVariantMap params;
                    params["trace_id"] = ConfigManager::instance()->get("testId").toString(); // 测试标识符
                    params["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate); // 建议用 ISO 格式
                    params["tension_value"] = resultFloat; // 实际拉力值，单位 KN
                    QString sql = R"(
                                  INSERT INTO tension_measurement (trace_id, timestamp, tension_value)
                                  VALUES (:trace_id, :timestamp, :tension_value)
                                  )";
                    bool ok = DatabaseManager::instance()->execTransactional(sql, params);
                    if (!ok) {
                        LogManager::instance()->writeLog("写入拉力数据失败", LogLevel::Error);
                    }
                    emit tensionDataInserted(QDateTime::currentDateTime(),resultFloat);
                } else {
                    LogManager::instance()->writeLog("运行状态读取失败：" + reply->errorString(), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送运行状态读取请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("拉力读取流程异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("拉力读取流程发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::readTheDisplacementValueAndReturnTheSignal()
{
    try {
        //        int statusAddress = ConfigManager::instance()->get("plc_displacementTime").toInt();
        //两个16位的寄存器拼接成一个32位的二进制数，将二进制数据解释为 IEEE 754 格式的浮点数
        QModbusDataUnit unit(QModbusDataUnit::HoldingRegisters, 204, 2);//10进制 高低位 转32位浮点

        if (auto* reply = modbusClient->sendReadRequest(unit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    quint16 status = reply->result().value(0);
                    quint16 status1 = reply->result().value(1);
                    quint32 combined = (static_cast<quint32>(status1) << 16) | status;
                    float resultFloat;
                    memcpy(&resultFloat, &combined, sizeof(resultFloat));
                    resultFloat = round(resultFloat * 100.0f) / 100.0f;

                    QVariantMap params;
                    params["trace_id"] = ConfigManager::instance()->get("testId").toString(); // 测试标识符
                    params["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate); // 建议用 ISO 格式
                    params["displacement_value"] = resultFloat; // 实际位移值，单位 mm
                    QString sql = R"(
                                  INSERT INTO displacement_measurement (trace_id, timestamp, displacement_value)
                                  VALUES (:trace_id, :timestamp, :displacement_value)
                                  )";
                    bool ok = DatabaseManager::instance()->execTransactional(sql, params);
                    if (!ok) {
                        LogManager::instance()->writeLog("写入位移数据失败", LogLevel::Error);
                    }
                    emit displacementDataInsertion(resultFloat);
                } else {
                    LogManager::instance()->writeLog("运行状态读取失败：" + reply->errorString(), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送运行状态读取请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("位移读取流程异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("位移读取流程发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeStartHighVoltageSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }
        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeCloseHighVoltageSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeLoadSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeUnistallSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeResetSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeStopSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeContinueSignal(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeStartExperimenting(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::writeStopExperimenting(int address, quint16 value)
{
    try {
        if(!modbusClient){
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        QModbusDataUnit writeUnit(QModbusDataUnit::HoldingRegisters, address-1, 1);
        writeUnit.setValue(0, value);

        if (auto* reply = modbusClient->sendWriteRequest(writeUnit, 1)) {
            connect(reply, &QModbusReply::finished, this, [=]() {
                if (reply->error() == QModbusDevice::NoError) {
                    LogManager::instance()->writeLog(QString("写入成功，地址[%1] 值[%2]").arg(address).arg(value), LogLevel::Debug);
                } else {
                    LogManager::instance()->writeLog(QString("写入失败：%1").arg(reply->errorString()), LogLevel::Warning);
                }
                reply->deleteLater();
            });
        } else {
            LogManager::instance()->writeLog("发送写入请求失败", LogLevel::Error);
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("写入寄存器过程中发生异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("写入寄存器过程中发生未知异常", LogLevel::Error);
    }
}

void ModbusManager::plcConnectState(QModbusDevice::State state)
{
    try {
        if (state == QModbusDevice::ConnectedState) {
            LogManager::instance()->writeLog("PLC 已成功连接", LogLevel::Info);
            qDebug()<<"PLC 已成功连接";
            plcConnectFlag = true;
            connectTimer->stop();
            //            conditionMonitoring->start(1000); // 启动心跳
            emit connedtedFromPLC();
        } else {
            LogManager::instance()->writeLog("PLC 连接异常", LogLevel::Info);
            qDebug()<<"PLC 连接异常";
            emit disconnectedFromPLC();
            plcConnectFlag = false;

            conditionMonitoring->stop();      // 停止心跳
            // 如果仍处于“连接中”状态，则强制断开
            if (modbusClient->state() == QModbusDevice::ConnectingState) {
                LogManager::instance()->writeLog("连接过程异常，强制断开重连", LogLevel::Warning);
                qDebug()<<"连接过程异常，强制断开重连";
                modbusClient->disconnectDevice();
            }
            connectTimer->start(3000); // 开启重连
        }
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("PLC 状态处理异常：" + QString::fromStdString(e.what()), LogLevel::Error);
        qDebug() << "PLC 状态处理异常：" << e.what();
    } catch (...) {
        LogManager::instance()->writeLog("PLC 状态处理发生未知异常", LogLevel::Error);
        qDebug() << "PLC 状态处理发生未知异常";
    }
}

void ModbusManager::plcConnectError(QModbusDevice::Error error)
{
    QString errorStr = modbusClient->errorString(); // 获取错误描述
    qDebug()<<"Modbus 错误："<<errorStr;
    LogManager::instance()->writeLog(QString("Modbus 错误 [%1]: %2").arg(error).arg(errorStr), LogLevel::Error);
}

void ModbusManager::checkHeartbeat()
{
    try {
        if (!modbusClient) {
            LogManager::instance()->writeLog("Modbus 客户端未初始化", LogLevel::Error);
            return;
        }

        if (modbusClient->state() != QModbusDevice::ConnectedState) {
            LogManager::instance()->writeLog("心跳检测失败：Modbus未连接", LogLevel::Warning);
            qDebug()<<"心跳检测失败：Modbus未连接";
            conditionMonitoring->stop();             // 停止心跳定时器
            connectTimer->start(3000);              // 启动重连定时器
            return;
        }

        int heartbeatAddress = ConfigManager::instance()->get("plc_heartbeat_address").toInt(); // 从配置读取地址
        // 创建数据单元
        QModbusDataUnit unit(QModbusDataUnit::HoldingRegisters, heartbeatAddress-1, 1);

        // 发送读取请求
        QModbusReply* reply = modbusClient->sendReadRequest(unit, 1);
        if (!reply) {
            LogManager::instance()->writeLog("发送读取请求失败：ModbusReply为空", LogLevel::Error);
            heartbeatFailureCount++;
            return;
        }
        // 响应处理
        connect(reply, &QModbusReply::finished, this, [=]() {
            try {
                if (reply->error() == QModbusDevice::NoError) {
                    const QModbusDataUnit result = reply->result();
                    QString logStr = QString("读取成功：地址[%1]，数量[%2]，值：").arg(heartbeatAddress).arg(1);
                    LogManager::instance()->writeLog(logStr, LogLevel::Info);
                    heartbeatFailureCount = 0; // 成功则清零
                } else {
                    LogManager::instance()->writeLog(QString("读取失败 [%1]：%2").arg(reply->error()).arg(reply->errorString()),LogLevel::Warning);
                    heartbeatFailureCount++; // 失败次数累计
                    if(heartbeatFailureCount >= heartbeatFailureThreshold){
                        LogManager::instance()->writeLog("连续心跳失败，触发断连重连机制", LogLevel::Warning);
                        conditionMonitoring->stop();
                        modbusClient->disconnectDevice();
                        connectTimer->start(3000);
                        heartbeatFailureCount = 0;
                    }
                }
            }  catch (const std::exception& e) {
                LogManager::instance()->writeLog("心跳回调处理异常：" + QString::fromStdString(e.what()), LogLevel::Error);
            } catch (...) {
                LogManager::instance()->writeLog("心跳回调处理发生未知异常", LogLevel::Error);
            }
            reply->deleteLater();
        });
    } catch (const std::exception& e) {
        LogManager::instance()->writeLog("心跳检测流程异常：" + QString::fromStdString(e.what()), LogLevel::Error);
    } catch (...) {
        LogManager::instance()->writeLog("心跳检测流程发生未知异常", LogLevel::Error);
    }
}
