#include "configuringreportdialog.h"

ConfiguringReportDialog::ConfiguringReportDialog(const QStringList &fieldNames, QWidget *parent)
    : QDialog(parent), m_fieldNames(fieldNames)
{
    setModal(true);
    setWindowTitle("参数配置");
    resize(360, 220);

    setupLayout();
    centerDialog();
}

void ConfiguringReportDialog::setupLayout()
{
    mainLayout = new QVBoxLayout(this);

    for (const QString &labelText : m_fieldNames) {
        QHBoxLayout *lineLayout = new QHBoxLayout();

        QLabel *label = new QLabel(labelText, this);
        QLineEdit *edit = new QLineEdit(this);
        m_lineEdits.append(edit);

        lineLayout->addWidget(label);
        lineLayout->addWidget(edit);
        mainLayout->addLayout(lineLayout);
    }

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    btnSave = new QPushButton("保存", this);
    btnCancel = new QPushButton("取消", this);

    buttonLayout->addStretch();
    buttonLayout->addWidget(btnSave);
    buttonLayout->addWidget(btnCancel);
    mainLayout->addLayout(buttonLayout);

    connect(btnSave, &QPushButton::clicked, this, &ConfiguringReportDialog::onSaveClicked);
    connect(btnCancel, &QPushButton::clicked, this, &ConfiguringReportDialog::onCancelClicked);
}

void ConfiguringReportDialog::centerDialog()
{
    QWidget *parent = parentWidget();
    QRect parentRect = parent ? parent->geometry() : QApplication::desktop()->screenGeometry();
    int x = parentRect.center().x() - width() / 2;
    int y = parentRect.center().y() - height() / 2;
    move(x, y);
}

void ConfiguringReportDialog::onSaveClicked()
{
    QSettings settings("config.ini", QSettings::IniFormat);
    for (int i = 0; i < m_fieldNames.size(); ++i) {
        settings.setValue(m_fieldNames[i], m_lineEdits[i]->text());
    }
    QMessageBox::information(this,"配置文件","数据保存成功");
    accept(); // 关闭对话框并返回 QDialog::Accepted
}

void ConfiguringReportDialog::onCancelClicked()
{
    reject(); // 关闭对话框并返回 QDialog::Rejected
}

QMap<QString, QString> ConfiguringReportDialog::getConfigValues() const
{
    QMap<QString, QString> values;
    for (int i = 0; i < m_fieldNames.size(); ++i) {
        values[m_fieldNames[i]] = m_lineEdits[i]->text();
    }
    return values;
}
