#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

/*
//初始化配置文件系统
ConfigManager::instance()->load();
// 读取字段
QString name = ConfigManager::instance()->get("operatorName").toString();
double force = ConfigManager::instance()->get("maxForce").toDouble();
int samples = ConfigManager::instance()->get("sampleCount").toInt();
LogManager::instance()->writeLog(name, LogLevel::Info);
LogManager::instance()->writeLog(QString::number(force), LogLevel::Info);
LogManager::instance()->writeLog(QString::number(samples), LogLevel::Info);
// 设置字段（自动插入或覆盖）
ConfigManager::instance()->set("operatorName", "文哥");
ConfigManager::instance()->set("sampleCount", 12);
ConfigManager::instance()->set("newOption", true); // 自动新增字段

ConfigManager::instance()->save();
LogManager::instance()->writeLog("成功保存！", LogLevel::Info);
*/
#include <QObject>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QVariant>
#include <QDir>
#include <QDebug>

#include "logmanager.h"

class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager* instance();              // 获取单例
    bool load();                                   // 加载配置文件
    bool save();                                   // 保存配置到文件

    QVariant get(const QString& key);           // 获取字段
    void set(const QString& key, const QVariant& value); // 设置字段（自动插入或更新）

private:
    ConfigManager();
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    QString configFilePath;
    QJsonObject configObject;
};

#endif // CONFIGMANAGER_H
